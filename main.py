import discord
from discord import app_commands, Intents, Client, TextChannel
from discord.ext import tasks
import asyncio
import logging
import sys

# -- Project Modules --
# Configuration (ensure this is loaded first)
from config import config

# Services and Managers
from bot_service import bot_service
from memory_manager import memory_manager
from message_cache import message_cache_manager

# Utilities
from discord_utils import (
    is_allowed_channel,
    is_allowed_user,
    message_to_dict,
    clean_message_content,
)

# --- Logging Setup --- #
# Configure root logger for the application
logging.basicConfig(
    level=logging.INFO,  # Set your desired app level (INFO, DEBUG, etc.)
    format="%(asctime)s:%(levelname)s:%(name)s: %(message)s",
    # Use force=True to potentially override any conflicting default configs
    force=True,
)
# Get the application's logger
logger = logging.getLogger(__name__)
# Discord.py will use its own logging setup or log to root based on its internal config.

# --- Discord Bot Setup --- #
intents = Intents.default()
intents.members = True  # Required for get_member, display names
intents.message_content = True  # Required to read message content

bot = Client(intents=intents)
tree = app_commands.CommandTree(bot)

# --- Event Handlers --- #


@bot.event
async def on_ready():
    if bot.user is None:
        logger.critical("Bot user information not available on ready. Cannot proceed.")
        return

    logger.info(f"Logged in as {bot.user.name} (ID: {bot.user.id})")
    logger.info(f"Test Mode: {config.test_mode}")
    logger.info(f"Allowed Servers: {config.allowed_server_ids}")
    logger.info(f"Allowed Channels: {config.allowed_channel_names}")
    logger.info(f"React-Only Channels: {config.react_only_channels}")

    bot_service.set_self_id(bot.user.id)
    await memory_manager.load_memory()

    await _populate_initial_cache()
    await _sync_commands()
    logger.info("Triggering initial LLM check for configured channels on startup...")
    await _queue_startup_llm_checks()

    # Start the periodic inactivity checker
    periodic_inactivity_checker.start()
    logger.info("Periodic inactivity checker started.")


@bot.event
async def on_message(message: discord.Message):
    if not await _should_process_message(message):
        return
    try:
        # Cast to TextChannel for type checker
        channel: TextChannel = message.channel  # type: ignore
        msg_dict = await _prepare_cache_message(message)
        await message_cache_manager.add_message(channel.id, msg_dict)
        logger.debug(f"Added message {message.id} to cache for #{channel.name}")
        asyncio.create_task(
            bot_service.process_channel_event(channel, trigger_type="new_message")
        )
    except Exception as e:
        logger.exception(f"Error during message processing: {e}")


@bot.event
async def on_message_delete(message: discord.Message):
    """Handles removing deleted messages from the cache."""
    # Ignore deletes in non-text channels or DMs
    if not isinstance(message.channel, TextChannel) or not message.guild:
        return

    # Check if the channel is one we monitor
    if is_allowed_channel(message.channel):
        # Cast to TextChannel for type checker
        channel: TextChannel = message.channel  # type: ignore
        logger.debug(
            f"Message {message.id} deleted in allowed channel #{channel.name}. Attempting cache removal."
        )
        # Attempt to remove the message from the cache
        # Run as a background task to avoid blocking the event loop if removal is slow
        asyncio.create_task(
            message_cache_manager.delete_message(channel.id, message.id)
        )
    # else:
    #     logger.debug(f"Ignoring message delete in non-allowed channel: #{message.channel.name}")


@bot.event
async def on_message_edit(before: discord.Message, after: discord.Message):
    """Handles updating edited messages in the cache."""
    # Ignore edits by bots or in non-text channels/DMs
    if (
        after.author.bot
        or not isinstance(after.channel, TextChannel)
        or not after.guild
    ):
        return

    # Check if the channel is one we monitor
    if is_allowed_channel(after.channel):
        # Cast to TextChannel for type checker
        channel: TextChannel = after.channel  # type: ignore
        # Avoid processing if content hasn't actually changed (e.g., embed edits)
        if before.content == after.content:
            return

        logger.debug(
            f"Message {after.id} edited in allowed channel #{channel.name}. Attempting cache update."
        )
        # Attempt to update the message in the cache
        # Run as a background task
        asyncio.create_task(message_cache_manager.update_message(channel.id, after))
    # else:
    #     logger.debug(f"Ignoring message edit in non-allowed channel: #{after.channel.name}")


@bot.event
async def on_raw_reaction_add(payload: discord.RawReactionActionEvent):
    """Update cache when reactions are added."""
    # Only handle for allowed guilds and channels
    if payload.guild_id not in config.allowed_server_ids:
        return
    channel = bot.get_channel(payload.channel_id)
    from discord import TextChannel

    if not isinstance(channel, TextChannel) or not is_allowed_channel(channel):
        return
    try:
        message = await channel.fetch_message(payload.message_id)
        # Update cached message to include new reactions
        asyncio.create_task(message_cache_manager.update_message(channel.id, message))
    except Exception:
        logger.exception(
            f"Failed to update cache for reaction add on msg {payload.message_id}"
        )


@bot.event
async def on_raw_reaction_remove(payload: discord.RawReactionActionEvent):
    """Update cache when reactions are removed."""
    if payload.guild_id not in config.allowed_server_ids:
        return
    channel = bot.get_channel(payload.channel_id)
    from discord import TextChannel

    if not isinstance(channel, TextChannel) or not is_allowed_channel(channel):
        return
    try:
        message = await channel.fetch_message(payload.message_id)
        asyncio.create_task(message_cache_manager.update_message(channel.id, message))
    except Exception:
        logger.exception(
            f"Failed to update cache for reaction remove on msg {payload.message_id}"
        )


# --- New Periodic Inactivity Checker --- #
@tasks.loop(minutes=1)  # Check every minute
async def periodic_inactivity_checker():
    logger.debug("Running periodic inactivity check...")
    check_tasks = []
    for server_id in config.allowed_server_ids:
        guild = bot.get_guild(server_id)
        if not guild:
            logger.warning(f"Periodic check: allowed server {server_id} not found.")
            continue
        # Iterate through the primary SOURCE channels configured
        for channel_name in config.allowed_channel_names:
            # Skip react-only channels for periodic inactivity checks
            if channel_name in config.react_only_channels:
                logger.debug(
                    f"Skipping periodic inactivity check for react-only channel: #{channel_name}"
                )
                continue

            channel = discord.utils.get(guild.text_channels, name=channel_name)
            if channel:
                # Delegate the check logic to the bot service
                check_tasks.append(bot_service.check_channel_inactivity(channel))
            else:
                logger.warning(
                    f"Periodic check: Source channel '{channel_name}' not found in guild {server_id}."
                )
    if check_tasks:
        await asyncio.gather(*check_tasks)
        logger.debug(
            "Periodic inactivity check finished for configured source channels."
        )
    else:
        logger.debug("Periodic inactivity check: No source channels found to check.")


@periodic_inactivity_checker.before_loop
async def before_periodic_check():
    await bot.wait_until_ready()  # Ensure bot is ready before starting the loop
    logger.info("Bot ready, periodic inactivity checker starting soon.")


# --- Slash Commands --- #


@tree.command(name="ping", description="Check if John is alive")
async def ping_slash(interaction: discord.Interaction):
    if not is_allowed_user(interaction.user):
        return await interaction.response.send_message("Unauthorized.", ephemeral=True)
    await interaction.response.send_message("Pong!", ephemeral=True)


@tree.command(
    name="llm", description="Manually trigger LLM processing on channel history"
)
async def llm_slash(interaction: discord.Interaction):
    if not is_allowed_user(interaction.user):
        return await interaction.response.send_message("Unauthorized.", ephemeral=True)

    # Ensure channel is a TextChannel and is allowed
    if not isinstance(interaction.channel, TextChannel) or not is_allowed_channel(
        interaction.channel
    ):
        return await interaction.response.send_message(
            "This command cannot be used in this channel.", ephemeral=True
        )

    await interaction.response.defer(ephemeral=True, thinking=True)
    logger.info(
        f"Manual LLM trigger initiated by {interaction.user.name} in #{interaction.channel.name}"
    )
    # Run in background task
    asyncio.create_task(
        bot_service.process_channel_event(
            interaction.channel, trigger_type="slash_command"
        )
    )
    await interaction.followup.send("LLM processing triggered.", ephemeral=True)


# --- Helper Functions --- #


async def _populate_initial_cache():
    cache_tasks = []
    for server_id in config.allowed_server_ids:
        guild = bot.get_guild(server_id)
        if not guild:
            logger.error(f"Could not find allowed guild with ID: {server_id}")
            continue
        for ch_name in config.allowed_channel_names:
            ch = discord.utils.get(guild.text_channels, name=ch_name)
            if ch:
                cutoff = config.channel_cutoff_map.get(ch.name)
                cache_tasks.append(
                    message_cache_manager.populate_initial_cache(
                        ch, config.initial_fetch_limit, start_message_id=cutoff
                    )
                )
    if cache_tasks:
        await asyncio.gather(*cache_tasks)
        logger.info("Initial cache population completed.")
    else:
        logger.warning("No channels queued for initial cache.")


async def _sync_commands():
    try:
        for server_id in config.allowed_server_ids:
            await tree.sync(guild=discord.Object(id=server_id))
        logger.info("Commands synced for allowed servers.")
    except Exception as e:
        logger.error(f"Error syncing commands: {e}")


async def _queue_startup_llm_checks():
    startup_tasks = []
    for server_id in config.allowed_server_ids:
        guild = bot.get_guild(server_id)
        if not guild:
            continue
        for channel_name in config.allowed_channel_names:
            # Skip react-only channels for startup LLM checks
            if channel_name in config.react_only_channels:
                logger.info(
                    f"Skipping startup LLM check for react-only channel: #{channel_name}"
                )
                continue

            channel = discord.utils.get(guild.text_channels, name=channel_name)
            if channel:
                startup_tasks.append(
                    bot_service.process_channel_event(
                        channel, trigger_type="bot_startup"
                    )
                )
    if startup_tasks:
        asyncio.gather(*startup_tasks)
        logger.info("Startup LLM checks queued.")
    else:
        logger.info(
            "No startup LLM checks queued (all channels are react-only or not found)."
        )


async def _should_process_message(message: discord.Message) -> bool:
    if (
        message.author.bot
        or not isinstance(message.channel, TextChannel)
        or not message.guild
    ):
        return False
    if not is_allowed_channel(message.channel):
        return False
    cleaned = clean_message_content(message.content)
    if not cleaned:
        return False
    return True


async def _prepare_cache_message(message: discord.Message) -> dict:
    # Ensure guild is present for type checker
    guild = message.guild  # type: ignore
    assert guild is not None
    msg_dict = await message_to_dict(message, guild)
    msg_dict["content"] = clean_message_content(message.content)
    return msg_dict


# --- Main Execution --- #

if __name__ == "__main__":
    if not config.discord_token:
        logger.critical(
            "CRITICAL: DISCORD_BOT_TOKEN is not set in the environment or .env file."
        )
        sys.exit(1)  # Exit if token is missing

    try:
        bot.run(config.discord_token)
    except discord.LoginFailure:
        logger.critical("CRITICAL: Failed to log in. Check the DISCORD_BOT_TOKEN.")
    except Exception as e:
        logger.critical(f"CRITICAL: Bot failed to run. Error: {e}", exc_info=True)
