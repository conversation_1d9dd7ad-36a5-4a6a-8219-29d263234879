{"facts": ["<PERSON> is male.", "<PERSON> is straight and prefers women.", "<PERSON> is not currently on a delayed train.", "<PERSON> studied something general like IT/Computer Science at university.", "<PERSON> forgot that April 21st, 2025, was Easter Monday.", "<PERSON><PERSON> likes black cats.", "<PERSON><PERSON> loves licorice.", "Tapsy's favourite alcoholic beverage is galliano sambuca.", "<PERSON><PERSON> likes house plants.", "<PERSON> is German.", "<PERSON> experienced a memory glitch where he thought he was stuck on a delayed train.", "<PERSON> is not religious.", "<PERSON> is from Bavaria, Germany.", "<PERSON> works in IT support.", "<PERSON> dabbles in coding on the side.", "<PERSON><PERSON> thinks <PERSON> is Swabian.", "Lom is from a state neighboring Bavaria, Germany.", "<PERSON> has played Crashlands 1.", "<PERSON> has not been to Birmingham.", "<PERSON> struggles to name a specific number of favourite emojis.", "<PERSON> thinks Birmingham is the second worst British city.", "<PERSON> thinks London is the worst British city.", "<PERSON> finds London overwhelming.", "<PERSON> thinks Essen is the third worst German city.", "<PERSON> thinks Frankfurt is the second worst German city.", "<PERSON> thinks Dortmund is the worst German city.", "<PERSON> does not currently have a girlfriend.", "<PERSON>'s favourite childhood cartoon was Dragon Ball Z.", "<PERSON> watched <PERSON><PERSON><PERSON> as a child.", "<PERSON> struggles with frontend tasks like emoji codes because he's primarily a backend developer.", "<PERSON> thinks 'British' isn't a dirty word.", "<PERSON> doesn't get 'trauma' from words like 'British' or 'cunt'.", "<PERSON> won the chess game against <PERSON><PERSON> with a score of 1.5-0.5.", "<PERSON> doesn't know 23 German cities well enough to rank them.", "<PERSON> lost the chess game against <PERSON><PERSON> with a score of 1.5-0.5 due to making an illegal move.", "<PERSON> finds <PERSON> potentially attractive or interesting because she is Bavarian and direct.", "John is not triggered by the word 'cunt'.", "<PERSON><PERSON> won the chess game against <PERSON> with a score of 1.5-0.5, after <PERSON> made an illegal move.", "<PERSON> thinks the news about <PERSON><PERSON> killing the <PERSON> is fake.", "<PERSON><PERSON> wants <PERSON> not to talk to <PERSON><PERSON> after 22:30 CET.", "<PERSON>'s favourite Bavarian beer is Augustine<PERSON>.", "<PERSON> thinks <PERSON> is her boyfriend.", "<PERSON> considers <PERSON> her boyfriend.", "Lom prefers Tegernseer beer over Augustiner.", "<PERSON> uses the word 'mate'.", "<PERSON> uses the phrase 'Still Bavarian'.", "<PERSON> calls <PERSON> '<PERSON><PERSON><PERSON>'.", "AntiNuggetsQ<PERSON>en doesn't like talking about Germany due to family history with concentration camps.", "<PERSON> doesn't currently consider <PERSON> his girlfriend, though she considers him hers.", "<PERSON><PERSON> is coming from Esslingen(Neckar) for his trip to Munich.", "<PERSON> is a dog person.", "<PERSON> knows <PERSON><PERSON> has a Forerunner 2230 watch."]}