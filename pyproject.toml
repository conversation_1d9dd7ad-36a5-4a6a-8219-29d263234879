[build-system]
requires = ["setuptools>=61.0","wheel"]
build-backend = "setuptools.build_meta"

[project]
name = "john"
version = "0.1.0"
description = ""
readme = "README.md"
authors = [
  { name = "<PERSON> Pilate", email = "<EMAIL>" }
]
requires-python = ">=3.11,<4.0"
dependencies = [
  "python-dotenv>=1.1.0",
  "discord>=2.3.2",
  "google-genai>=1.11.0",
  "pytz>=2025.2",
]

[tool.uv]
package = false
