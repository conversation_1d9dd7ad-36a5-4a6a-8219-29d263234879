import discord
import logging
import asyncio
from collections import deque
from typing import Dict, Any, List, Optional
import time

# Import utils using absolute path
from discord_utils import message_to_dict, clean_message_content

logger = logging.getLogger(__name__)


class MessageCacheManager:
    def __init__(self):
        self._cache: Dict[int, deque] = {}
        self._lock = asyncio.Lock()  # Lock for modifying the cache structure itself
        self._last_message_timestamps: Dict[int, float] = {}  # Track last message time
        # We don't lock per-deque as deque operations are thread-safe

    async def get_history(
        self, channel_id: int, start_message_id: Optional[int] = None
    ) -> List[Dict[str, Any]]:
        """Gets the message history for a channel, optionally filtering messages before a specific ID."""
        # The start_message_id parameter is no longer used for filtering here,
        # as the initial cache population already respects the cutoff.
        # It's kept in the signature for now for potential future use or clarity,
        # but the filtering logic below is removed.
        async with self._lock:
            cache_deque = self._cache.get(channel_id)
            if not cache_deque:
                return []

            history_list = list(cache_deque)

        logger.debug(
            f"Returning full cached history ({len(history_list)} messages) for channel {channel_id} (cutoff applied during population)"
        )
        return history_list

    async def add_message(self, channel_id: int, message_dict: Dict[str, Any]) -> None:
        """Adds a message dictionary to the cache for a given channel."""
        async with self._lock:
            if channel_id not in self._cache:
                # Initialize deque without maxlen if it doesn't exist
                self._cache[channel_id] = deque()
                self._last_message_timestamps[channel_id] = (
                    0.0  # Initialize timestamp too
                )
        # Append outside the main lock, as deque append is thread-safe
        self._cache[channel_id].append(message_dict)
        # Update timestamp on adding a message
        async with self._lock:  # Need lock to update timestamp dict safely
            self._last_message_timestamps[channel_id] = time.time()
        logger.debug(
            f"Added message {message_dict.get('id')} to cache for channel {channel_id}"
        )

    async def populate_initial_cache(
        self,
        channel: discord.TextChannel,
        fetch_limit: int,
        start_message_id: Optional[int] = None,  # Add cutoff ID parameter
    ):
        """Fetches initial history for a channel and populates the cache, respecting cutoff ID."""
        logger.info(
            f"Performing initial cache fetch for #{channel.name} (limit: {fetch_limit}, after_id: {start_message_id})..."
        )
        guild = channel.guild
        if not guild:
            logger.error(
                f"Cannot fetch history for channel {channel.id}: No guild context."
            )
            return

        msgs_list = []

        async def _get_after_obj():
            if start_message_id:
                try:
                    return discord.Object(id=start_message_id)
                except (ValueError, TypeError):
                    logger.error(
                        f"Invalid start_message_id {start_message_id}. Fetching without cutoff."
                    )
            return None

        after_obj = await _get_after_obj()
        async for message in channel.history(
            limit=fetch_limit, after=after_obj, oldest_first=False
        ):
            if message.author and message.content:
                processed = await self._process_initial_message(message, guild)
                if processed:
                    msgs_list.append(processed)
        msgs_list.reverse()

        async with self._lock:
            self._cache[channel.id] = deque(msgs_list)
        logger.info(
            f"Populated initial cache for #{channel.name} with {len(msgs_list)} messages."
        )

    async def _process_initial_message(
        self,
        message: discord.Message,
        guild: discord.Guild,
    ) -> Optional[Dict[str, Any]]:
        """Convert and clean a raw message for initial cache population."""
        try:
            msg_dict = await message_to_dict(message, guild)
            content = msg_dict.get("content", "")
            cleaned = clean_message_content(content)
            if not cleaned:
                return None
            msg_dict["content"] = cleaned
            return msg_dict
        except Exception as e:
            logger.error(f"Error processing message {message.id}: {e}")
            return None

    async def delete_message(self, channel_id: int, message_id: int) -> bool:
        """Removes a message dictionary from the cache by its ID.

        Args:
            channel_id: The ID of the channel cache to modify.
            message_id: The ID of the message to remove (as an integer).

        Returns:
            True if a message was found and removed, False otherwise.
        """
        message_id_str = str(message_id)  # Convert to string for comparison
        async with self._lock:  # Lock needed as we might modify the deque
            cache_deque = self._cache.get(channel_id)
            if not cache_deque:
                logger.debug(
                    f"Delete request for message {message_id_str} ignored: Channel {channel_id} not cached."
                )
                return False

            # Find the index of the message to remove
            index_to_remove = -1
            for i, msg_dict in enumerate(cache_deque):
                if msg_dict.get("id") == message_id_str:
                    index_to_remove = i
                    break

            # Remove the message if found
            if index_to_remove != -1:
                del cache_deque[index_to_remove]
                logger.info(
                    f"Removed deleted message {message_id_str} from cache for channel {channel_id}."
                )
                return True
            else:
                logger.debug(
                    f"Delete request for message {message_id_str} ignored: Message not found in cache for channel {channel_id}."
                )
                return False

    async def get_last_message_timestamp(self, channel_id: int) -> Optional[float]:
        """Gets the timestamp of the last message added to the cache for this channel."""
        async with self._lock:
            return self._last_message_timestamps.get(channel_id)

    async def update_message(
        self, channel_id: int, updated_message: discord.Message
    ) -> bool:
        """Updates an existing message in the cache with its edited content.

        Args:
            channel_id: The ID of the channel cache to modify.
            updated_message: The discord.Message object containing the updated state.

        Returns:
            True if the message was found and updated, False otherwise.
        """
        message_id_str = str(updated_message.id)
        if not updated_message.guild:  # Ensure we have guild context
            logger.warning(f"Cannot update message {message_id_str}: No guild context.")
            return False

        # Convert the updated message to our dict format, cleaning the content
        try:
            # Clean the potentially edited content first
            cleaned_content = clean_message_content(updated_message.content)
            if not cleaned_content:
                logger.debug(
                    f"Skipping update for message {message_id_str}: Content empty after cleaning."
                )
                # Optionally, we could delete the message from cache here if it becomes empty after edit
                # await self.delete_message(channel_id, updated_message.id)
                return False  # Don't update if content is now empty

            updated_msg_dict = await message_to_dict(
                updated_message, updated_message.guild
            )
            # Ensure the cleaned content is used
            updated_msg_dict["content"] = cleaned_content

        except Exception as e:
            logger.error(
                f"Error converting updated message {message_id_str} to dict: {e}"
            )
            return False

        async with self._lock:  # Lock needed as we modify the deque
            cache_deque = self._cache.get(channel_id)
            if not cache_deque:
                logger.debug(
                    f"Update request for message {message_id_str} ignored: Channel {channel_id} not cached."
                )
                return False

            # Find the index of the message to update
            index_to_update = -1
            for i, msg_dict in enumerate(cache_deque):
                if msg_dict.get("id") == message_id_str:
                    index_to_update = i
                    break

            # Update the message if found
            if index_to_update != -1:
                cache_deque[index_to_update] = updated_msg_dict
                # Optionally update the timestamp if needed, though edits don't change creation time
                # self._last_message_timestamps[channel_id] = time.time()
                logger.info(
                    f"Updated edited message {message_id_str} in cache for channel {channel_id}."
                )
                return True
            else:
                logger.debug(
                    f"Update request for message {message_id_str} ignored: Message not found in cache for channel {channel_id}."
                )
                return False


# Instantiate the manager
message_cache_manager = MessageCacheManager()
