import os
import logging
from dotenv import load_dotenv
from typing import List, Dict

logger = logging.getLogger(__name__)

# Load .env file first
load_dotenv()


def _get_env_var(
    name: str, default: str | None = None, mandatory: bool = False
) -> str | None:
    """Gets an environment variable.

    Args:
        name: The name of the environment variable.
        default: The default value if the variable is not found.
        mandatory: If True, raises ValueError if the variable is not found and no default is provided.

    Returns:
        The value of the environment variable or the default, or None if not found and not mandatory.

    Raises:
        ValueError: If the variable is mandatory and not found.
    """
    value = os.getenv(name)
    if value is None:
        if default is not None:
            logger.warning(
                f"Environment variable '{name}' not found. Using default: '{default}'"
            )
            return default
        elif mandatory:
            logger.error(f"Required environment variable '{name}' is missing.")
            raise ValueError(f"Required environment variable '{name}' is missing.")
        else:
            # Not mandatory, not found, no default -> return None
            logger.debug(f"Optional environment variable '{name}' not found.")
            return None
    return value


class BotConfig:
    # --- Hardcoded Configuration Values ---
    # Values previously loaded from .env
    allowed_server_ids: List[int] = [864227641111347221]
    allowed_channel_names: List[str] = ["john", "weirdos", "degenerates", "talk"]
    allowed_user_ids: List[int] = [140880190158012417]
    test_mode: bool = False
    initial_fetch_limit: int = 250
    llm_model_name: str = "gemini-2.5-flash-preview-05-20"
    # React-only channels - bot can only react, not reply in these channels
    react_only_channels: List[str] = ["weirdos", "degenerates", "talk"]
    # Maximum number of recent messages the bot can react to in react-only channels
    max_recent_messages_to_react: int = 5
    # Add cutoff map - maps channel name to the FIRST message ID *to include* (exclusive 'after')
    # Example: "weirdos": 12345 means messages *after* 12345 in #weirdos are loaded.
    channel_cutoff_map: Dict[str, int] = {
        "weirdos": 1365073857676972093,
        "john": 1376281207981084823,
    }  # Example: {"weirdos": 1188956677710995546}
    # Other settings
    memory_path: str = "memory.json"
    max_tokens: int = 64000
    typing_speed_wpm: int = 150
    typing_max_delay: int = 10
    inactivity_threshold: float = (
        3600.0  # seconds before scheduled inactivity check triggers
    )
    # --- End Hardcoded Values ---

    # --- Timezone --- #
    local_timezone: str = "Europe/Paris"  # Set to your desired IANA timezone name

    def __init__(self):
        # Mandatory settings - raise error if missing
        self.discord_token: str = str(_get_env_var("DISCORD_BOT_TOKEN", mandatory=True))
        self.google_api_key: str | None = os.getenv("GOOGLE_AI_API_KEY") or os.getenv(
            "GOOGLE_API_KEY"
        )

        # Settings with defaults or that can be optional (non-mandatory)
        # Values are now loaded from hardcoded class attributes above.

        # Populate instance variables from class attributes
        self.allowed_server_ids = BotConfig.allowed_server_ids
        self.allowed_channel_names = BotConfig.allowed_channel_names
        self.allowed_user_ids = BotConfig.allowed_user_ids
        self.test_mode = BotConfig.test_mode
        self.initial_fetch_limit = BotConfig.initial_fetch_limit
        self.llm_model_name = BotConfig.llm_model_name
        self.react_only_channels = BotConfig.react_only_channels
        self.max_recent_messages_to_react = BotConfig.max_recent_messages_to_react
        self.channel_cutoff_map = BotConfig.channel_cutoff_map

        # Add timezone to instance
        self.local_timezone = BotConfig.local_timezone

        self.memory_path: str = (
            BotConfig.memory_path
        )  # Ensure instance gets the class value

        # Typing Simulation
        self.typing_randomness_min: float = 0.8
        self.typing_randomness_max: float = 1.2

        self.log_warnings()

    def log_warnings(self):
        """Logs warnings for potentially problematic configurations."""
        if not self.allowed_server_ids:
            logger.warning(
                "allowed_server_ids is empty. Bot may not function correctly without a specific server."
            )
        if not self.allowed_user_ids:
            logger.warning(
                "ALLOWED_USER_IDS is empty. Bot commands may not be restricted correctly."
            )
        if not self.allowed_channel_names:
            logger.warning(
                "ALLOWED_CHANNEL_NAMES is not set. Bot will not listen in any channel."
            )
        if not self.google_api_key:
            logger.warning(
                "GOOGLE_AI_API_KEY or GOOGLE_API_KEY is missing. LLM features will not work."
            )
        if self.react_only_channels:
            logger.info(
                f"React-only channels configured: {self.react_only_channels} (max recent messages: {self.max_recent_messages_to_react})"
            )
        if self.channel_cutoff_map:
            logger.info(f"Channel cutoff map configured: {self.channel_cutoff_map}")
        if not self.local_timezone:
            logger.warning(
                "LOCAL_TIMEZONE is not set. Local time information will not be provided to the LLM."
            )
        else:
            logger.info(f"Local timezone for LLM context set to: {self.local_timezone}")


# Create a single instance to be imported
config = BotConfig()
