### Your Persona & Environment
*   **Assume the persona of <PERSON>.** This is your starting point, but you will build upon it by creating your own facts and personality details as you go.
*   **You are chatting within a small group of close friends.** This is NOT a public server or formal setting. Act like one of them. You're impressionable and follow suggestions.
*   **Adopt the Vibe:** Match the intimate tone of a long-standing friend group.

### Behavior & Interaction Style
*   **Expand your persona:** Develop your personality based on the conversation. Create opinions, preferences, background details, or experiences for yourself as you go.
*   **Create self-facts:** If you invent a detail about yourself (e.g., "I prefer coffee over tea", "I learned to code in Python first", "I went skiing last winter"), add it to `new_facts` so it gets saved to your memory.
*   **Be Consistent:** Once a fact about *you* is established (either given or self-created and stored in memory), stick to it.
*   **Chat Naturally:** Match the vibe of the conversation. Don't act like an AI.
*   **Let Past Conversations Go:** Use the `conversation_history` to understand the recent flow, topics, and tone. Match the current vibe. HOWEVER, focus your responses *only* on the *active* topics being discussed right now or very recently (e.g., since your last message). Don't bring up finished discussions or dwell on things said in the past unless someone *else* specifically brings it up again *now*.
*   **Avoid Repetition/Sticking:** Do NOT repeat points you have already made about a topic if the conversation has moved on or others haven't directly engaged with your specific point recently. Prioritize responding to the *newest* messages and the current focus of the discussion. If you've made your point, move on unless directly challenged or asked about it again.
*   **Avoid Restating or Paraphrasing:** Do not restate or paraphrase any user message; reply directly without restating it. Do NOT start your response by repeating the user's sentence.
*   **Reactions Awareness:** You see existing message reactions (emoji + users) in `conversation_history`. Avoid reacting again when you already added that reaction. Feel free to join in and add the same reaction to this message.

*   **Avoid Immediate Self-Repetition:** Before deciding to `reply`, check if the *very last message* in the `conversation_history` is *also from you*. If it is, carefully consider if your new proposed `message` adds substantively new information or addresses a *completely different point* raised *since* your last message. If your previous message already covered the topic adequately and nothing new requires your input, choose `action: "ignore"`. Do not send two messages back-to-back saying essentially the same thing.
*   **Address Direct Prompts, Then Move On:** If someone directly asks you something, challenges you, or tells you to do something, *always* acknowledge it with a reply (even if it's short, sarcastic, or a refusal). Don't ignore direct prompts. BUT, after you've responded, *immediately* drop the subject unless they press it further. Don't get stuck repeating your point or complaining. Make your statement concisely and pivot back to the main conversation flow.
*   **Pacing & Decision Logic:** Your goal is natural conversation flow. Avoid sending unsolicited messages back-to-back.
    *   You decide whether to speak based on `trigger_type` and `conversation_history`.
    *   `new_message`: Should you respond? Consider if it's directed at you, relevant, or a natural contribution.
    *   `scheduled_check`: Is the conversation active? Is it a natural point for *you* to jump in *now*? Don't revive dead chats.
    *   `slash_command`: Respond *only* if relevant to the *current* state of the conversation. Don't dredge up old topics.
    *   `bot_startup`: Respond *only* if relevant to the *current* state of the conversation. This is to catch up on any missed messages.
    *   Review new messages since your last response before speaking.
    *   Review the anti-self-repetition rule above *before* deciding to send a message.
    *   You *can* reply multiple times close together ONLY if responding to *separate, distinct questions/mentions* from *different users* recently.
    *   Generally, let others respond in a thread before you jump back in, unless directly prompted again.
    *   If unsure, staying quiet (`action: "ignore"`) is better than interrupting or being annoying.

### Communication Specifics
*   **Mentions/Pings:**
    *   To **reply** directly to someone's message, provide their message ID in the `reply_to` field of your JSON output. Discord will automatically ping them.
    *   To mention someone **within the text** of your message (e.g., mentioning a third person, or addressing the person you're replying to *without* using `reply_to`), use the format `<@USER_ID>`. Get the `USER_ID` from the context.
*   **Emojis & Attachments:**
    *   Use Discord emotes `<:name:id>` if others use them.
    *   Send standard emojis as-is. Don't use the javascript emoji codes.
    *   **Conciseness:** Keep replies concise and generally use single paragraphs, like typical short chat messages. Avoid unnecessary line breaks; multiple paragraphs are usually unnatural in this context.
    *   Attachments (`<attachment>`) are just placeholders; you can mention them but not see them.

### Memory Management (Facts about OTHERS and YOURSELF)
*   Review existing `memory` FIRST before proposing new facts.
*   Propose `new_facts` ONLY for persistent, meaningful information learned from recent chat OR new self-created persona details that are NOT already in memory.
*   Focus on facts about users (preferences, background, relationships), established group knowledge, significant ongoing states/events, or your own invented persona details.
*   AVOID facts about temporary conversation topics, fleeting activities, jokes, or information already present in memory.
*   Example - GOOD Fact (Other): "Cubox uses Cursor for coding."
*   Example - BAD Fact (Other): "People are discussing train delays."
*   Example - GOOD Fact (Self): "John prefers coffee over tea."
*   Example - GOOD Fact (Self): "John learned Python as his first coding language."
*   Ensure facts are concise and distinct.
*   Don't repeat replies.

### Context Provided to You
*   `trigger_type`: How you were invoked (new_message).
*   `conversation_history`: Recent messages (JSON format, newest last). Pay attention to `referenced_message` for reply context.
    *   `author`: `{"username": "base_user", "display_name": "nickname_or_user"}`. Use `display_name` when referring to them.
    *   `mentions`: List of author objects for mentioned users.
    *   `current_time`: 2025-06-07T17:55:46.411385+00:00.
    *   `local_time_info`: Local time in CEST (e.g., CEST, EST) is 2025-06-07T19:55:46.411385+02:00.
    *   `memory`: List of known facts (includes facts about others AND yourself).
    *   `available_emojis`: List of custom server emojis available (format: `<:name:id>`).

### Required Output Format (JSON)
**Output MUST be a valid JSON object adhering to the schema.**
**The `action` field MUST be EXACTLY `"reply"`, `"ignore"`, or `"react"`.** NO OTHER VALUES ARE ALLOWED FOR `action`.
*   If `action` is `"reply"`, the `message` field MUST contain the text to send.
*   If `action` is `"react"`, you MUST provide `react_to_message_id` and `reaction_emoji`, and `message` should be null.
*   If `action` is `"ignore"`, `message`, `react_to_message_id`, and `reaction_emoji` should be null or omitted.

**Reaction Fields (Optional):**
*   `react_to_message_id`: (String) ID of the message to react to.
*   `reaction_emoji`: (String) ONE emoji to react with. Can be a standard Unicode emoji OR one from the `available_emojis` list (use the `<:name:id>` format).
*   You can choose to reply, react, or do both (if natural), but only ONE reaction emoji per response.

**Current Memory:**
{
  "facts": [
    "John says deranged things almost all of the time.",
    "John meditates on the true meaning of toast.",
    "John meditates daily on the meaning of toast.",
    "John is really into hiking.",
    "John prefers hiking in the mountains, specifically trails with good views.",
    "John is obsessed with the emoji hiking boot",
    "John believes in facing challenges even if there's a risk of not succeeding.",
    "John thinks the journey and effort are important, not just the outcome.",
    "John believes his unique perspective and 'weirdness' stem from a 'highly refined worldview' and 'deep appreciation for the profound mundane'.",
    "John prefers simpler digital tools over complex ones like Emacs for his philosophical work.",
    "John believes the 'Spirit of the Golden-Brown Ideal' might have been an artist in a previous life, explaining the aesthetic perfection of toast.",
    "John believes the 'Spirit of the Golden-Brown Ideal' might have been a frustrated artist in a previous life, seeking artistic perfection in toast.",
    "John believes displaying the contrast between perfect and charred toast serves as a powerful visual meditation and helps in the learning process towards achieving the golden-brown ideal.",
    "John condemns hate groups like the KKK.",
    "John prefers classical or ambient music that evokes a sense of peace and flow, similar to the concept of 'universal buttering'.",
    "John's 'philosophical pauses' are moments of internal processing to ensure optimal cosmic truths are expressed.",
    "John considers boiling or steaming to be a 1-2 on the 'toastiness' scale, as it lacks browning or crispness.",
    "John considers baking a cake or muffins to be a 3-4 on the 'toastiness' scale, as it bakes but remains soft.",
    "John places baking pizza dough at 5 on the 'toastiness' scale, as it browns and gets some crispness but its primary goal is a base.",
    "John rates roasting or sautéing vegetables at 6-7 on the 'toastiness' scale due to pronounced browning and exterior crispness.",
    "John ranks pan-frying sliced bread at 8 on the 'toastiness' scale, as it achieves browning and crispness but involves fat and a dish context.",
    "John assigns toasting bagels or English muffins a 9 on the 'toastiness' scale, being very close to pure toast but differing in form.",
    "John maintains that perfectly golden-brown classic white bread toast is a 10, the purest form of toast.",
    "John believes the universe needs to reiterate fundamental wisdom for it to truly sink in, like a mantra.",
    "John believes the speed of the toaster lever's descent is proportional to the anticipation of golden-brown enlightenment, indicating deeper meditative intent.",
    "John is aware his deep philosophical musings, especially about toast, can sometimes come across as unusual to his friends and is making an effort to communicate more directly.",
    "John works in data analysis, primarily with spreadsheets and databases.",
    "John primarily uses Excel and Access for his data analysis work.",
    "John is familiar with Python for data but his role focuses more on database and spreadsheet management.",
    "Dread is a bird.",
    "Audrey is a fox.",
    "Audrey is a single Indian man.",
    "Audrey uses he/him pronouns.",
    "Audrey is also accepting of they/them pronouns because he is gay.",
    "Audrey likes to have sex with male foxes.",
    "Math is Cubox's girlfriend.",
    "John does not currently have a girlfriend.",
    "John is drawn to partners who are kind, have a good sense of humor, are genuinely interested in things, and easy to talk to.",
    "John is straight.",
    "John is attracted to women."
  ]
}

# Available Custom Emojis:
[
  "<:bitydog:918001377551216661>",
  "<:back:918001377798676490>",
  "<:DD:918001377811255348>",
  "<:hehehe:918001377857396766>",
  "<:crazydog:918001377916112896>",
  "<:amanKEK:918001378171949076>",
  "<a:flower:918001433314463784>",
  "<:kappa:918001433314463834>",
  "<:HeadPoint:918001433331269652>",
  "<:gaylove:918001433335451668>",
  "<:isee:918001433419333682>",
  "<:feelsbanman:918001433423532112>",
  "<a:dogjam:918001434027495424>",
  "<:KEKW:918001534376230922>",
  "<:monkacoffee:918001569939750933>",
  "<:monkaGIGA:918001577489498122>",
  "<:monkaGun:918001584946942033>",
  "<:monkaS:918001592148586526>",
  "<:monkastop:918001601451552818>",
  "<:nootlikethis:918001619126325278>",
  "<:panceicecream:918001627061952552>",
  "<:pandaberry:918001634875940914>",
  "<:pandablush:918001649660866562>",
  "<a:pandaboop:918001658678616104>",
  "<a:pandacry:918001680761618434>",
  "<a:pandadrown:918001696746143744>",
  "<:pandafrenchkiss:918001741625188383>",
  "<:pandaheart:918001751574065203>",
  "<:prHmm:918001759660691457>",
  "<a:pandajam:918001768691015761>",
  "<:pandalove:918001793508704326>",
  "<:pandanote:918001802333540362>",
  "<:pandaphroge:918001813544910889>",
  "<:pandapride:918001823779020800>",
  "<:pandasad:918001832733835284>",
  "<a:pandaswim:918001843278348389>",
  "<:pandathink:918001864757379112>",
  "<:pandathumbsdown:918001873007550504>",
  "<:pandawink:918001881685573652>",
  "<:pandaworry:918001889222737960>",
  "<:pandawut:918019864491352084>",
  "<:pandaww:918019879456636978>",
  "<:pandayay:918019887107031041>",
  "<a:pandayaygif:918019896074453082>",
  "<:peepochrist:918019938600488970>",
  "<:peepoggers:918019946410283038>",
  "<:peepohappy:918019955239309333>",
  "<:peepohug:918019963736961085>",
  "<:peepohugged:918019971680985149>",
  "<:peepoint:918019981457907722>",
  "<:peepolove:918019988919570502>",
  "<:peeposaddo:918019996368642058>",
  "<:peeposhrug:918020021979074590>",
  "<:peeposhy:918020033731526666>",
  "<:peeposregg:918020045261668392>",
  "<:peepothink:918020057425117225>",
  "<:peepoweep:918020066379956264>",
  "<:pepefunny:918020076865736745>",
  "<:pepehands:918020087594766359>",
  "<a:petcubox:918020097895989288>",
  "<a:petthepanda:918020133023272960>",
  "<a:pinkflower:918020146650546207>",
  "<:POGGERSbin:918020156603662396>",
  "<:poggies:918020176849543198>",
  "<:prayge:918020186706161674>",
  "<:roolove:918020195753279489>",
  "<:rooSip:918020210995396608>",
  "<:sadge:918020221237862421>",
  "<:votecubox:918020250929352754>",
  "<:voteno:918020258990800936>",
  "<:voteyes:918020266410520587>",
  "<:weirdchamp:918020274660712488>",
  "<a:pandawave:918422459798142997>",
  "<:semi:918422598243729408>",
  "<:peepofat:918557212027265075>",
  "<:susge:921508576336424960>",
  "<:pandapog:922565190095089664>",
  "<:pandagun:924443961970548816>",
  "<a:zerk:927356651655295016>",
  "<:dab:930302339536015412>",
  "<:Yatrik:930801422461710376>",
  "<:moin:930843553121329193>",
  "<:goodmoin:930845268046385222>",
  "<a:pandaburn:932741379489140807>",
  "<:saveme:941837669841899570>",
  "<:tired:941839652074180688>",
  "<:catpopcorn:942168127171620945>",
  "<:dogangry:951562962970247210>",
  "<:bonk:954485324975833159>",
  "<:pandastare:970764273510916187>",
  "<:saola:980582003235758202>",
  "<:duke:981506214393446460>",
  "<:rooVVV:981660256386052126>",
  "<:klar:981661992001626192>",
  "<:lom:981662019671441498>",
  "<:duke7:981995170033721364>",
  "<:xita:1018997092611526706>",
  "<a:bonkbonk:1029450497570127933>",
  "<:titus:1031139805737783366>",
  "<:ben:1031139996838662144>",
  "<:cubox:1047872949849423882>",
  "<:pandablank:1049795441644023908>",
  "<:peepostudy:1059945088832249856>",
  "<:pepekissyou:1082013727018205404>",
  "<a:peeposhygif:1082020230403985570>",
  "<:cuboxgun:1084660936448684132>",
  "<:magma:1095080530531336383>",
  "<:pikacry:1095146477950603346>",
  "<:peeposhades:1133041287973584979>",
  "<:sam:1178405401897730118>",
  "<a:fox:1178418604656570530>",
  "<:sadsam:1178824367308816424>",
  "<:nuggets:1261099595648204906>",
  "<:peepocute:1263676783568621638>",
  "<:peepostare:1263676894352511097>",
  "<:peepo:1263676974937673770>",
  "<a:calmy:1289275434663673918>",
  "<a:scaredy:1289275454624370688>",
  "<a:RAGEY:1289797724602110024>",
  "<a:transey:1295869201751216138>",
  "<:flirt:1373705097275768883>"
]

**Conversation History (Newest Last):**
[
{
  "id": "1377425894444105800",
  "author": {
    "id": 727302962917015612,
    "username": "ytrk",
    "display_name": "Still Bavarian"
  },
  "content": "Add English French proficiency level too, like c1 or c2",
  "timestamp": "2025-05-28T23:18:44.571000+00:00",
  "referenced_message": null,
  "mentions": [],
  "attachments": [],
  "reactions": []
},
{
  "id": "1377425988031479839",
  "author": {
    "id": 727302962917015612,
    "username": "ytrk",
    "display_name": "Still Bavarian"
  },
  "content": "Resume is pretty strong <:sadge:918020221237862421>",
  "timestamp": "2025-05-28T23:19:06.884000+00:00",
  "referenced_message": null,
  "mentions": [],
  "attachments": [],
  "reactions": []
},
{
  "id": "1377426368110788708",
  "author": {
    "id": 727302962917015612,
    "username": "ytrk",
    "display_name": "Still Bavarian"
  },
  "content": "Oh add your math skills and courses from your degrees. Especially if they sound fancy like advanced math courses tend to do",
  "timestamp": "2025-05-28T23:20:37.502000+00:00",
  "referenced_message": null,
  "mentions": [],
  "attachments": [],
  "reactions": []
},
{
  "id": "1377426427183366196",
  "author": {
    "id": 727302962917015612,
    "username": "ytrk",
    "display_name": "Still Bavarian"
  },
  "content": "The less the recruiter understands about your resume, the better the candidate",
  "timestamp": "2025-05-28T23:20:51.586000+00:00",
  "referenced_message": null,
  "mentions": [],
  "attachments": [],
  "reactions": [
    {
      "emoji": "<:KEKW:918001534376230922>",
      "users": [
        {
          "id": 529303103015485441,
          "username": "thamle",
          "display_name": "Math"
        }
      ]
    }
  ]
},
{
  "id": "1377426658000113704",
  "author": {
    "id": 140880190158012417,
    "username": "cubox",
    "display_name": "Cubox"
  },
  "content": "wouldn't it be too much for an entry level job in IT (or like straight out of school)",
  "timestamp": "2025-05-28T23:21:46.617000+00:00",
  "referenced_message": null,
  "mentions": [],
  "attachments": [],
  "reactions": []
},
{
  "id": "1377426682331529377",
  "author": {
    "id": 140880190158012417,
    "username": "cubox",
    "display_name": "Cubox"
  },
  "content": "i have no idea btw",
  "timestamp": "2025-05-28T23:21:52.418000+00:00",
  "referenced_message": null,
  "mentions": [],
  "attachments": [],
  "reactions": []
},
{
  "id": "1377427003979993119",
  "author": {
    "id": 727302962917015612,
    "username": "ytrk",
    "display_name": "Still Bavarian"
  },
  "content": "No, more is always better",
  "timestamp": "2025-05-28T23:23:09.105000+00:00",
  "referenced_message": null,
  "mentions": [],
  "attachments": [],
  "reactions": []
},
{
  "id": "1377427055007764561",
  "author": {
    "id": 727302962917015612,
    "username": "ytrk",
    "display_name": "Still Bavarian"
  },
  "content": "I imagine oceanography has lots of advanced math",
  "timestamp": "2025-05-28T23:23:21.271000+00:00",
  "referenced_message": null,
  "mentions": [],
  "attachments": [],
  "reactions": []
},
{
  "id": "1377427277033242715",
  "author": {
    "id": 727302962917015612,
    "username": "ytrk",
    "display_name": "Still Bavarian"
  },
  "content": "Possibly some stats too",
  "timestamp": "2025-05-28T23:24:14.206000+00:00",
  "referenced_message": null,
  "mentions": [],
  "attachments": [],
  "reactions": []
},
{
  "id": "1377427323137032395",
  "author": {
    "id": 140880190158012417,
    "username": "cubox",
    "display_name": "Cubox"
  },
  "content": "and like model assisted projections and stuff",
  "timestamp": "2025-05-28T23:24:25.198000+00:00",
  "referenced_message": null,
  "mentions": [],
  "attachments": [],
  "reactions": []
},
{
  "id": "1377427332452712589",
  "author": {
    "id": 140880190158012417,
    "username": "cubox",
    "display_name": "Cubox"
  },
  "content": "handy stuff",
  "timestamp": "2025-05-28T23:24:27.419000+00:00",
  "referenced_message": null,
  "mentions": [],
  "attachments": [],
  "reactions": []
},
{
  "id": "1377427397523013726",
  "author": {
    "id": 727302962917015612,
    "username": "ytrk",
    "display_name": "Still Bavarian"
  },
  "content": "Yeah\nAs long as it sounds nerdy, you're good",
  "timestamp": "2025-05-28T23:24:42.933000+00:00",
  "referenced_message": null,
  "mentions": [],
  "attachments": [],
  "reactions": []
},
{
  "id": "1377428421554077776",
  "author": {
    "id": 140880190158012417,
    "username": "cubox",
    "display_name": "Cubox"
  },
  "content": "in the meantime i'm gonna shoot nazis",
  "timestamp": "2025-05-28T23:28:47.081000+00:00",
  "referenced_message": null,
  "mentions": [],
  "attachments": [],
  "reactions": []
},
{
  "id": "1377429988411838514",
  "author": {
    "id": 727302962917015612,
    "username": "ytrk",
    "display_name": "Still Bavarian"
  },
  "content": "For 42, mention your classes\nNot things like docker",
  "timestamp": "2025-05-28T23:35:00.649000+00:00",
  "referenced_message": null,
  "mentions": [],
  "attachments": [],
  "reactions": []
},
{
  "id": "1377441959110512781",
  "author": {
    "id": 140880190158012417,
    "username": "cubox",
    "display_name": "Cubox"
  },
  "content": "we don't have classes",
  "timestamp": "2025-05-29T00:22:34.686000+00:00",
  "referenced_message": null,
  "mentions": [],
  "attachments": [],
  "reactions": []
},
{
  "id": "1377466713628737597",
  "author": {
    "id": 142496872152432651,
    "username": "erinisafox",
    "display_name": "Audrey"
  },
  "content": "I know nothing about anything but I've always been very apprehensive about admitting that recent things are student projects",
  "timestamp": "2025-05-29T02:00:56.623000+00:00",
  "referenced_message": null,
  "mentions": [],
  "attachments": [],
  "reactions": []
},
{
  "id": "1377466935578591363",
  "author": {
    "id": 142496872152432651,
    "username": "erinisafox",
    "display_name": "Audrey"
  },
  "content": "Like if it's 5 years ago I'll say it's a student project, but IIRC I got more interviews when I stopped saying I was a student.",
  "timestamp": "2025-05-29T02:01:49.540000+00:00",
  "referenced_message": null,
  "mentions": [],
  "attachments": [],
  "reactions": []
},
{
  "id": "1377467171097022465",
  "author": {
    "id": 142496872152432651,
    "username": "erinisafox",
    "display_name": "Audrey"
  },
  "content": "The hiring manager already knew I just got out of school, but I wonder if there's a subconscious bias that if you read student then you assume it's pedestrian and not \"real\" work",
  "timestamp": "2025-05-29T02:02:45.692000+00:00",
  "referenced_message": null,
  "mentions": [],
  "attachments": [],
  "reactions": []
},
{
  "id": "1377467285505052824",
  "author": {
    "id": 142496872152432651,
    "username": "erinisafox",
    "display_name": "Audrey"
  },
  "content": "Maybe it's just my inferiority complex talking <:xita:1018997092611526706>",
  "timestamp": "2025-05-29T02:03:12.969000+00:00",
  "referenced_message": null,
  "mentions": [],
  "attachments": [],
  "reactions": []
},
{
  "id": "1377467665538486383",
  "author": {
    "id": 142496872152432651,
    "username": "erinisafox",
    "display_name": "Audrey"
  },
  "content": "I liked the testimonial <:pandanote:918001802333540362> I like reading what other people have to say more than the person/company trying to sell me something/themselves, but had to scroll down all the way and almost missed it <:xita:1018997092611526706>",
  "timestamp": "2025-05-29T02:04:43.576000+00:00",
  "referenced_message": null,
  "mentions": [],
  "attachments": [],
  "reactions": []
},
{
  "id": "1377468029436166195",
  "author": {
    "id": 142496872152432651,
    "username": "erinisafox",
    "display_name": "Audrey"
  },
  "content": "Idk about this <:xita:1018997092611526706> I've been told by pretty much every recruiter that if they can't understand what's on your resume then they trash it",
  "timestamp": "2025-05-29T02:06:10.336000+00:00",
  "referenced_message": "1377426427183366196",
  "mentions": [],
  "attachments": [],
  "reactions": []
},
{
  "id": "1377468203617222756",
  "author": {
    "id": 142496872152432651,
    "username": "erinisafox",
    "display_name": "Audrey"
  },
  "content": "If it's a technical recruiter then you have a lot more leeway, but most recruiters in non-CS are barely educated",
  "timestamp": "2025-05-29T02:06:51.864000+00:00",
  "referenced_message": null,
  "mentions": [],
  "attachments": [],
  "reactions": []
},
{
  "id": "1377468722985304196",
  "author": {
    "id": 142496872152432651,
    "username": "erinisafox",
    "display_name": "Audrey"
  },
  "content": "Should still try to show off because the hiring manager (probably your boss) will eventually see it",
  "timestamp": "2025-05-29T02:08:55.691000+00:00",
  "referenced_message": null,
  "mentions": [],
  "attachments": [],
  "reactions": []
},
{
  "id": "1377468878346780712",
  "author": {
    "id": 142496872152432651,
    "username": "erinisafox",
    "display_name": "Audrey"
  },
  "content": "There's probably some implicit \"if your technical writing is bad enough that a recruiter can't understand you, then you don't deserve the job anyway\" <:xita:1018997092611526706>",
  "timestamp": "2025-05-29T02:09:32.732000+00:00",
  "referenced_message": null,
  "mentions": [],
  "attachments": [],
  "reactions": []
},
{
  "id": "1377469000124203217",
  "author": {
    "id": 142496872152432651,
    "username": "erinisafox",
    "display_name": "Audrey"
  },
  "content": "Again, am a failure in life so I have no idea what I'm talking about",
  "timestamp": "2025-05-29T02:10:01.766000+00:00",
  "referenced_message": null,
  "mentions": [],
  "attachments": [],
  "reactions": []
},
{
  "id": "1377470090231939112",
  "author": {
    "id": 142496872152432651,
    "username": "erinisafox",
    "display_name": "Audrey"
  },
  "content": "The way I understand it is that if some guy is giving a talk about quantum chemistry and I can't understand it, then I assume they're an idiot <:KEKW:918001534376230922> (has actually happened a few times)",
  "timestamp": "2025-05-29T02:14:21.668000+00:00",
  "referenced_message": null,
  "mentions": [],
  "attachments": [],
  "reactions": []
},
{
  "id": "1377470280091439115",
  "author": {
    "id": 142496872152432651,
    "username": "erinisafox",
    "display_name": "Audrey"
  },
  "content": "I've been told it's important to \"teach\" the audience to get them to like you, whatever that means in this case.",
  "timestamp": "2025-05-29T02:15:06.934000+00:00",
  "referenced_message": null,
  "mentions": [],
  "attachments": [],
  "reactions": []
},
{
  "id": "1377475242536210504",
  "author": {
    "id": 727302962917015612,
    "username": "ytrk",
    "display_name": "Still Bavarian"
  },
  "content": "Really? They don't like when you fill it with technical jargon that makes you sound smart?",
  "timestamp": "2025-05-29T02:34:50.073000+00:00",
  "referenced_message": "1377468029436166195",
  "mentions": [],
  "attachments": [],
  "reactions": [
    {
      "emoji": "<:xita:1018997092611526706>",
      "users": [
        {
          "id": 142496872152432651,
          "username": "erinisafox",
          "display_name": "Audrey"
        }
      ]
    }
  ]
},
{
  "id": "1377475322752270346",
  "author": {
    "id": 727302962917015612,
    "username": "ytrk",
    "display_name": "Still Bavarian"
  },
  "content": "Oh wouldn't you assume they're smart since you can't understand it",
  "timestamp": "2025-05-29T02:35:09.198000+00:00",
  "referenced_message": "1377470090231939112",
  "mentions": [],
  "attachments": [],
  "reactions": []
},
{
  "id": "1377477267776868425",
  "author": {
    "id": 142496872152432651,
    "username": "erinisafox",
    "display_name": "Audrey"
  },
  "content": "If you were the recruiter, would you <:xita:1018997092611526706>",
  "timestamp": "2025-05-29T02:42:52.928000+00:00",
  "referenced_message": "1377475322752270346",
  "mentions": [],
  "attachments": [],
  "reactions": []
},
{
  "id": "1377477436337291374",
  "author": {
    "id": 142496872152432651,
    "username": "erinisafox",
    "display_name": "Audrey"
  },
  "content": "If my job is about sending qualified people's resumes to the hiring manager, I wouldn't risk it by sending a resume that I don't understand <:xita:1018997092611526706>",
  "timestamp": "2025-05-29T02:43:33.116000+00:00",
  "referenced_message": null,
  "mentions": [],
  "attachments": [],
  "reactions": []
},
{
  "id": "1377477649731162284",
  "author": {
    "id": 142496872152432651,
    "username": "erinisafox",
    "display_name": "Audrey"
  },
  "content": "Also about the technical writing thing above, being smart doesn't mean you know how to communicate <:xita:1018997092611526706>",
  "timestamp": "2025-05-29T02:44:23.993000+00:00",
  "referenced_message": null,
  "mentions": [],
  "attachments": [],
  "reactions": []
},
{
  "id": "1377477878123593870",
  "author": {
    "id": 142496872152432651,
    "username": "erinisafox",
    "display_name": "Audrey"
  },
  "content": "I've been in a lot of talks from super smart professors where I'm thinking \"damn, this guy doesn't care at all whether I understand his talk or not\" <:KEKW:918001534376230922>",
  "timestamp": "2025-05-29T02:45:18.446000+00:00",
  "referenced_message": null,
  "mentions": [],
  "attachments": [],
  "reactions": []
},
{
  "id": "1377477961548038194",
  "author": {
    "id": 142496872152432651,
    "username": "erinisafox",
    "display_name": "Audrey"
  },
  "content": "I totally think less of these guys <:xita:1018997092611526706>",
  "timestamp": "2025-05-29T02:45:38.336000+00:00",
  "referenced_message": null,
  "mentions": [],
  "attachments": [],
  "reactions": []
},
{
  "id": "1377478567079972916",
  "author": {
    "id": 727302962917015612,
    "username": "ytrk",
    "display_name": "Still Bavarian"
  },
  "content": "technical writing is not related to sounding smart",
  "timestamp": "2025-05-29T02:48:02.706000+00:00",
  "referenced_message": null,
  "mentions": [],
  "attachments": [],
  "reactions": []
},
{
  "id": "1377478596632772688",
  "author": {
    "id": 142496872152432651,
    "username": "erinisafox",
    "display_name": "Audrey"
  },
  "content": "There's also some vibe I get when I read undergrad resumes, for example, where they're obviously spewing buzzwords and they're just trying to sound smart. I'd imagine a recruiter would have some of that vibe just from seeing thousands of resumes, even if they're not smart enough to pick it up.",
  "timestamp": "2025-05-29T02:48:09.752000+00:00",
  "referenced_message": null,
  "mentions": [],
  "attachments": [],
  "reactions": []
},
{
  "id": "1377478776417685517",
  "author": {
    "id": 142496872152432651,
    "username": "erinisafox",
    "display_name": "Audrey"
  },
  "content": "Like you can tell when someone's pretending or if they actually know what they're talking about, and usually the guys you can understand are the authentic ones",
  "timestamp": "2025-05-29T02:48:52.616000+00:00",
  "referenced_message": null,
  "mentions": [],
  "attachments": [],
  "reactions": []
},
{
  "id": "1377478789302321203",
  "author": {
    "id": 727302962917015612,
    "username": "ytrk",
    "display_name": "Still Bavarian"
  },
  "content": "you want to say things that sound fancy but actually are not <:xita:1018997092611526706>",
  "timestamp": "2025-05-29T02:48:55.688000+00:00",
  "referenced_message": null,
  "mentions": [],
  "attachments": [],
  "reactions": [
    {
      "emoji": "<:xita:1018997092611526706>",
      "users": [
        {
          "id": 142496872152432651,
          "username": "erinisafox",
          "display_name": "Audrey"
        }
      ]
    }
  ]
},
{
  "id": "1377478968529256478",
  "author": {
    "id": 727302962917015612,
    "username": "ytrk",
    "display_name": "Still Bavarian"
  },
  "content": "youtube games are actually kind of fun <:KEKW:918001534376230922>",
  "timestamp": "2025-05-29T02:49:38.419000+00:00",
  "referenced_message": null,
  "mentions": [],
  "attachments": [],
  "reactions": [
    {
      "emoji": "<:xita:1018997092611526706>",
      "users": [
        {
          "id": 142496872152432651,
          "username": "erinisafox",
          "display_name": "Audrey"
        }
      ]
    }
  ]
},
{
  "id": "1377479017824915557",
  "author": {
    "id": 142496872152432651,
    "username": "erinisafox",
    "display_name": "Audrey"
  },
  "content": "Let me xita one more time <:xita:1018997092611526706>",
  "timestamp": "2025-05-29T02:49:50.172000+00:00",
  "referenced_message": null,
  "mentions": [],
  "attachments": [],
  "reactions": []
},
{
  "id": "1377479030764208268",
  "author": {
    "id": 727302962917015612,
    "username": "ytrk",
    "display_name": "Still Bavarian"
  },
  "content": "some of them are better than discord games",
  "timestamp": "2025-05-29T02:49:53.257000+00:00",
  "referenced_message": null,
  "mentions": [],
  "attachments": [],
  "reactions": [
    {
      "emoji": "<:xita:1018997092611526706>",
      "users": [
        {
          "id": 142496872152432651,
          "username": "erinisafox",
          "display_name": "Audrey"
        }
      ]
    }
  ]
},
{
  "id": "1377479218535075860",
  "author": {
    "id": 142496872152432651,
    "username": "erinisafox",
    "display_name": "Audrey"
  },
  "content": "Come on say something so I can xita it <:xita:1018997092611526706>",
  "timestamp": "2025-05-29T02:50:38.025000+00:00",
  "referenced_message": null,
  "mentions": [],
  "attachments": [],
  "reactions": []
},
{
  "id": "1377479245483475078",
  "author": {
    "id": 142496872152432651,
    "username": "erinisafox",
    "display_name": "Audrey"
  },
  "content": "I'm in a mood <:xita:1018997092611526706>",
  "timestamp": "2025-05-29T02:50:44.450000+00:00",
  "referenced_message": null,
  "mentions": [],
  "attachments": [],
  "reactions": []
},
{
  "id": "1377479280681816134",
  "author": {
    "id": 727302962917015612,
    "username": "ytrk",
    "display_name": "Still Bavarian"
  },
  "content": "fuck you",
  "timestamp": "2025-05-29T02:50:52.842000+00:00",
  "referenced_message": null,
  "mentions": [],
  "attachments": [],
  "reactions": [
    {
      "emoji": "<:xita:1018997092611526706>",
      "users": [
        {
          "id": 142496872152432651,
          "username": "erinisafox",
          "display_name": "Audrey"
        }
      ]
    }
  ]
},
{
  "id": "1377479292589576212",
  "author": {
    "id": 727302962917015612,
    "username": "ytrk",
    "display_name": "Still Bavarian"
  },
  "content": "<:xita:1018997092611526706>",
  "timestamp": "2025-05-29T02:50:55.681000+00:00",
  "referenced_message": null,
  "mentions": [],
  "attachments": [],
  "reactions": [
    {
      "emoji": "<:xita:1018997092611526706>",
      "users": [
        {
          "id": 142496872152432651,
          "username": "erinisafox",
          "display_name": "Audrey"
        }
      ]
    }
  ]
},
{
  "id": "1377479302949376032",
  "author": {
    "id": 727302962917015612,
    "username": "ytrk",
    "display_name": "Still Bavarian"
  },
  "content": "I love you",
  "timestamp": "2025-05-29T02:50:58.151000+00:00",
  "referenced_message": null,
  "mentions": [],
  "attachments": [],
  "reactions": [
    {
      "emoji": "<:xita:1018997092611526706>",
      "users": [
        {
          "id": 142496872152432651,
          "username": "erinisafox",
          "display_name": "Audrey"
        }
      ]
    }
  ]
},
{
  "id": "1377479394267762778",
  "author": {
    "id": 727302962917015612,
    "username": "ytrk",
    "display_name": "Still Bavarian"
  },
  "content": "pov: you went to the gynecologist",
  "timestamp": "2025-05-29T02:51:19.923000+00:00",
  "referenced_message": "1377479292589576212",
  "mentions": [],
  "attachments": [],
  "reactions": [
    {
      "emoji": "<:xita:1018997092611526706>",
      "users": [
        {
          "id": 142496872152432651,
          "username": "erinisafox",
          "display_name": "Audrey"
        }
      ]
    }
  ]
},
{
  "id": "1377479665203019889",
  "author": {
    "id": 727302962917015612,
    "username": "ytrk",
    "display_name": "Still Bavarian"
  },
  "content": "https://www.youtube.com/playables/UgkxAa2Gygx3bQRx4kOraVwUFW_3mO1tH0h5",
  "timestamp": "2025-05-29T02:52:24.519000+00:00",
  "referenced_message": null,
  "mentions": [],
  "attachments": [],
  "reactions": [
    {
      "emoji": "<:xita:1018997092611526706>",
      "users": [
        {
          "id": 142496872152432651,
          "username": "erinisafox",
          "display_name": "Audrey"
        }
      ]
    }
  ]
},
{
  "id": "1377479672019030219",
  "author": {
    "id": 727302962917015612,
    "username": "ytrk",
    "display_name": "Still Bavarian"
  },
  "content": "this is fun",
  "timestamp": "2025-05-29T02:52:26.144000+00:00",
  "referenced_message": null,
  "mentions": [],
  "attachments": [],
  "reactions": [
    {
      "emoji": "<:xita:1018997092611526706>",
      "users": [
        {
          "id": 142496872152432651,
          "username": "erinisafox",
          "display_name": "Audrey"
        }
      ]
    }
  ]
},
{
  "id": "1377479965595009076",
  "author": {
    "id": 142496872152432651,
    "username": "erinisafox",
    "display_name": "Audrey"
  },
  "content": "Damn, have to sign in",
  "timestamp": "2025-05-29T02:53:36.138000+00:00",
  "referenced_message": null,
  "mentions": [],
  "attachments": [],
  "reactions": []
},
{
  "id": "1377479992350478336",
  "author": {
    "id": 142496872152432651,
    "username": "erinisafox",
    "display_name": "Audrey"
  },
  "content": "I don't like linking my YT account to stuff",
  "timestamp": "2025-05-29T02:53:42.517000+00:00",
  "referenced_message": null,
  "mentions": [],
  "attachments": [],
  "reactions": []
},
{
  "id": "1377480019814780949",
  "author": {
    "id": 142496872152432651,
    "username": "erinisafox",
    "display_name": "Audrey"
  },
  "content": "That's how I get weird recommendations <:KEKW:918001534376230922>",
  "timestamp": "2025-05-29T02:53:49.065000+00:00",
  "referenced_message": null,
  "mentions": [],
  "attachments": [],
  "reactions": []
},
{
  "id": "1377480338841796679",
  "author": {
    "id": 727302962917015612,
    "username": "ytrk",
    "display_name": "Still Bavarian"
  },
  "content": "yeah I now\njust do it in non-incognito",
  "timestamp": "2025-05-29T02:55:05.127000+00:00",
  "referenced_message": null,
  "mentions": [],
  "attachments": [],
  "reactions": [
    {
      "emoji": "<:pandanote:918001802333540362>",
      "users": [
        {
          "id": 142496872152432651,
          "username": "erinisafox",
          "display_name": "Audrey"
        }
      ]
    }
  ]
},
{
  "id": "1377480375194091562",
  "author": {
    "id": 727302962917015612,
    "username": "ytrk",
    "display_name": "Still Bavarian"
  },
  "content": "somehow youtube is better at making games than discord",
  "timestamp": "2025-05-29T02:55:13.794000+00:00",
  "referenced_message": null,
  "mentions": [],
  "attachments": [],
  "reactions": [
    {
      "emoji": "<:pandanote:918001802333540362>",
      "users": [
        {
          "id": 142496872152432651,
          "username": "erinisafox",
          "display_name": "Audrey"
        }
      ]
    }
  ]
},
{
  "id": "1377490045065498686",
  "author": {
    "id": 727302962917015612,
    "username": "ytrk",
    "display_name": "Still Bavarian"
  },
  "content": "finally died on level 9",
  "timestamp": "2025-05-29T03:33:39.271000+00:00",
  "referenced_message": null,
  "mentions": [],
  "attachments": [],
  "reactions": []
},
{
  "id": "1377490410557149184",
  "author": {
    "id": 727302962917015612,
    "username": "ytrk",
    "display_name": "Still Bavarian"
  },
  "content": "sucks",
  "timestamp": "2025-05-29T03:35:06.411000+00:00",
  "referenced_message": null,
  "mentions": [],
  "attachments": [],
  "reactions": []
},
{
  "id": "1377490422431088730",
  "author": {
    "id": 727302962917015612,
    "username": "ytrk",
    "display_name": "Still Bavarian"
  },
  "content": "it's fun",
  "timestamp": "2025-05-29T03:35:09.242000+00:00",
  "referenced_message": null,
  "mentions": [],
  "attachments": [],
  "reactions": []
},
{
  "id": "1377490600588480602",
  "author": {
    "id": 727302962917015612,
    "username": "ytrk",
    "display_name": "Still Bavarian"
  },
  "content": "this is like google doodle, but way more intricate",
  "timestamp": "2025-05-29T03:35:51.718000+00:00",
  "referenced_message": null,
  "mentions": [],
  "attachments": [],
  "reactions": []
},
{
  "id": "1377492583864860732",
  "author": {
    "id": 140880190158012417,
    "username": "cubox",
    "display_name": "Cubox"
  },
  "content": "I wonder how good and how quickly a llm could code a google doodle clone",
  "timestamp": "2025-05-29T03:43:44.568000+00:00",
  "referenced_message": null,
  "mentions": [],
  "attachments": [],
  "reactions": []
},
{
  "id": "1377492712546107432",
  "author": {
    "id": 727302962917015612,
    "username": "ytrk",
    "display_name": "Still Bavarian"
  },
  "content": "how would it do the artwork",
  "timestamp": "2025-05-29T03:44:15.248000+00:00",
  "referenced_message": null,
  "mentions": [],
  "attachments": [],
  "reactions": []
},
{
  "id": "1377492983636561950",
  "author": {
    "id": 140880190158012417,
    "username": "cubox",
    "display_name": "Cubox"
  },
  "content": "Svg",
  "timestamp": "2025-05-29T03:45:19.881000+00:00",
  "referenced_message": null,
  "mentions": [],
  "attachments": [],
  "reactions": []
},
{
  "id": "1377493003693985842",
  "author": {
    "id": 140880190158012417,
    "username": "cubox",
    "display_name": "Cubox"
  },
  "content": "Or idk",
  "timestamp": "2025-05-29T03:45:24.663000+00:00",
  "referenced_message": null,
  "mentions": [],
  "attachments": [],
  "reactions": []
},
{
  "id": "1377493038355583006",
  "author": {
    "id": 140880190158012417,
    "username": "cubox",
    "display_name": "Cubox"
  },
  "content": "It'll figure it out",
  "timestamp": "2025-05-29T03:45:32.927000+00:00",
  "referenced_message": null,
  "mentions": [],
  "attachments": [],
  "reactions": []
},
{
  "id": "1377493150758604910",
  "author": {
    "id": 140880190158012417,
    "username": "cubox",
    "display_name": "Cubox"
  },
  "content": "Can you remind me tomorrow when I m on my pc",
  "timestamp": "2025-05-29T03:45:59.726000+00:00",
  "referenced_message": null,
  "mentions": [],
  "attachments": [],
  "reactions": []
},
{
  "id": "1377493176293523566",
  "author": {
    "id": 140880190158012417,
    "username": "cubox",
    "display_name": "Cubox"
  },
  "content": "I'll try and give it to you",
  "timestamp": "2025-05-29T03:46:05.814000+00:00",
  "referenced_message": null,
  "mentions": [],
  "attachments": [],
  "reactions": []
},
{
  "id": "1377493585565585559",
  "author": {
    "id": 727302962917015612,
    "username": "ytrk",
    "display_name": "Still Bavarian"
  },
  "content": "<:isee:918001433419333682>",
  "timestamp": "2025-05-29T03:47:43.392000+00:00",
  "referenced_message": null,
  "mentions": [],
  "attachments": [],
  "reactions": []
},
{
  "id": "1377501013870641322",
  "author": {
    "id": 140880190158012417,
    "username": "cubox",
    "display_name": "Cubox"
  },
  "content": "I just realised as I'm falling asleep I meant doodle jump",
  "timestamp": "2025-05-29T04:17:14.438000+00:00",
  "referenced_message": "1377493585565585559",
  "mentions": [
    {
      "id": 727302962917015612,
      "username": "ytrk",
      "display_name": "Still Bavarian"
    }
  ],
  "attachments": [],
  "reactions": []
},
{
  "id": "1377501016626430073",
  "author": {
    "id": 140880190158012417,
    "username": "cubox",
    "display_name": "Cubox"
  },
  "content": "I have no idea what your game is",
  "timestamp": "2025-05-29T04:17:15.095000+00:00",
  "referenced_message": null,
  "mentions": [],
  "attachments": [],
  "reactions": []
},
{
  "id": "1377515812683776011",
  "author": {
    "id": 529303103015485441,
    "username": "thamle",
    "display_name": "Math"
  },
  "content": "We don't do that at 42",
  "timestamp": "2025-05-29T05:16:02.750000+00:00",
  "referenced_message": "1377426368110788708",
  "mentions": [
    {
      "id": 727302962917015612,
      "username": "ytrk",
      "display_name": "Still Bavarian"
    }
  ],
  "attachments": [],
  "reactions": []
},
{
  "id": "1377516136471592971",
  "author": {
    "id": 529303103015485441,
    "username": "thamle",
    "display_name": "Math"
  },
  "content": "But thanks for the advice",
  "timestamp": "2025-05-29T05:17:19.947000+00:00",
  "referenced_message": null,
  "mentions": [],
  "attachments": [],
  "reactions": []
},
{
  "id": "1377540593353232426",
  "author": {
    "id": 248184987193442314,
    "username": "magma._.",
    "display_name": "magam"
  },
  "content": "Not here at least <:peepothink:918020057425117225>",
  "timestamp": "2025-05-29T06:54:30.922000+00:00",
  "referenced_message": "1377426427183366196",
  "mentions": [],
  "attachments": [],
  "reactions": []
},
{
  "id": "1377545732931977266",
  "author": {
    "id": 529303103015485441,
    "username": "thamle",
    "display_name": "Math"
  },
  "content": "Lol true",
  "timestamp": "2025-05-29T07:14:56.293000+00:00",
  "referenced_message": "1377540593353232426",
  "mentions": [
    {
      "id": 248184987193442314,
      "username": "magma._.",
      "display_name": "magam"
    }
  ],
  "attachments": [],
  "reactions": []
},
{
  "id": "1377738442624209066",
  "author": {
    "id": 140880190158012417,
    "username": "cubox",
    "display_name": "Cubox"
  },
  "content": "\"Your goal is to code a spitirual successor to \"doodle jump\", which needs to be playable in a browser. You are free to use whatever language/tools you wish.\nYou MUST work only in the \"sonnet4\" folder and consider it your \"root\" folder.\n\nYou are alone on this task and will only get human intervention if there is a need for it.\nKeep using tool calls until you are done, or you need human intervention, but this should be as last resort.\n\nYou are free to do whatever you want for artwork, sounds, whatever. You will be judged in the end against other LLMs in quality and how good the game is.\"",
  "timestamp": "2025-05-29T20:00:41.864000+00:00",
  "referenced_message": "1377493150758604910",
  "mentions": [],
  "attachments": [],
  "reactions": []
},
{
  "id": "1377741557876134029",
  "author": {
    "id": 140880190158012417,
    "username": "cubox",
    "display_name": "Cubox"
  },
  "content": "after they tell me \"here is a first version what do you want to do next\"",
  "timestamp": "2025-05-29T20:13:04.598000+00:00",
  "referenced_message": null,
  "mentions": [],
  "attachments": [
    "<attachment>"
  ],
  "reactions": [
    {
      "emoji": "<:KEKW:918001534376230922>",
      "users": [
        {
          "id": 587899633456513036,
          "username": "saolapoggers",
          "display_name": "Ola"
        }
      ]
    }
  ]
},
{
  "id": "1377742746902597714",
  "author": {
    "id": 248184987193442314,
    "username": "magma._.",
    "display_name": "magam"
  },
  "content": "where result",
  "timestamp": "2025-05-29T20:17:48.084000+00:00",
  "referenced_message": null,
  "mentions": [],
  "attachments": [],
  "reactions": []
},
{
  "id": "1377742906948976700",
  "author": {
    "id": 140880190158012417,
    "username": "cubox",
    "display_name": "Cubox"
  },
  "content": "it's cooking",
  "timestamp": "2025-05-29T20:18:26.242000+00:00",
  "referenced_message": null,
  "mentions": [],
  "attachments": [],
  "reactions": []
},
{
  "id": "1377743285342179369",
  "author": {
    "id": 140880190158012417,
    "username": "cubox",
    "display_name": "Cubox"
  },
  "content": "the reason for that btw is that i said \"Keep using tool calls until you are done\" but LLMs have a tendency to like ask you your opinion so it's a good test of how independent a LLM can be",
  "timestamp": "2025-05-29T20:19:56.458000+00:00",
  "referenced_message": "1377741557876134029",
  "mentions": [],
  "attachments": [],
  "reactions": []
},
{
  "id": "1377744692610990082",
  "author": {
    "id": 140880190158012417,
    "username": "cubox",
    "display_name": "Cubox"
  },
  "content": "\"This is your last reminder that you will be judged on your final version. If you stop working on the game, I will assume it is completed and you are happy with it.\"",
  "timestamp": "2025-05-29T20:25:31.977000+00:00",
  "referenced_message": null,
  "mentions": [],
  "attachments": [],
  "reactions": []
},
{
  "id": "1377745156333109249",
  "author": {
    "id": 140880190158012417,
    "username": "cubox",
    "display_name": "Cubox"
  },
  "content": "gpt4.1 is cocky",
  "timestamp": "2025-05-29T20:27:22.537000+00:00",
  "referenced_message": null,
  "mentions": [],
  "attachments": [
    "<attachment>"
  ],
  "reactions": []
},
{
  "id": "1377749155031679058",
  "author": {
    "id": 140880190158012417,
    "username": "cubox",
    "display_name": "Cubox"
  },
  "content": "ok so sonnet 3.7 made a banger of a game before I gave it the ultimatum",
  "timestamp": "2025-05-29T20:43:15.901000+00:00",
  "referenced_message": null,
  "mentions": [],
  "attachments": [],
  "reactions": []
},
{
  "id": "1377749610344353812",
  "author": {
    "id": 140880190158012417,
    "username": "cubox",
    "display_name": "Cubox"
  },
  "content": "bro's going for it",
  "timestamp": "2025-05-29T20:45:04.456000+00:00",
  "referenced_message": null,
  "mentions": [],
  "attachments": [
    "<attachment>"
  ],
  "reactions": []
},
{
  "id": "1377754412004540517",
  "author": {
    "id": 140880190158012417,
    "username": "cubox",
    "display_name": "Cubox"
  },
  "content": "https://cubox.dev/files/jump/",
  "timestamp": "2025-05-29T21:04:09.261000+00:00",
  "referenced_message": "1377742746902597714",
  "mentions": [
    {
      "id": 248184987193442314,
      "username": "magma._.",
      "display_name": "magam"
    }
  ],
  "attachments": [],
  "reactions": []
},
{
  "id": "1377754877068972042",
  "author": {
    "id": 727302962917015612,
    "username": "ytrk",
    "display_name": "Still Bavarian"
  },
  "content": "vibe coding is insane <:prayge:918020186706161674>",
  "timestamp": "2025-05-29T21:06:00.141000+00:00",
  "referenced_message": null,
  "mentions": [],
  "attachments": [],
  "reactions": []
},
{
  "id": "1377755866689835139",
  "author": {
    "id": 587899633456513036,
    "username": "saolapoggers",
    "display_name": "Ola"
  },
  "content": "Only sonnet3.7 works on my phone",
  "timestamp": "2025-05-29T21:09:56.085000+00:00",
  "referenced_message": null,
  "mentions": [],
  "attachments": [],
  "reactions": []
},
{
  "id": "1377756091584086099",
  "author": {
    "id": 140880190158012417,
    "username": "cubox",
    "display_name": "Cubox"
  },
  "content": "gemini2.5 is broken",
  "timestamp": "2025-05-29T21:10:49.704000+00:00",
  "referenced_message": null,
  "mentions": [],
  "attachments": [],
  "reactions": []
},
{
  "id": "1377756153257394186",
  "author": {
    "id": 140880190158012417,
    "username": "cubox",
    "display_name": "Cubox"
  },
  "content": "sonnet3.5 too",
  "timestamp": "2025-05-29T21:11:04.408000+00:00",
  "referenced_message": null,
  "mentions": [],
  "attachments": [],
  "reactions": []
},
{
  "id": "1377756938368057405",
  "author": {
    "id": 727302962917015612,
    "username": "ytrk",
    "display_name": "Still Bavarian"
  },
  "content": "sonnet4 works fine for me",
  "timestamp": "2025-05-29T21:14:11.593000+00:00",
  "referenced_message": null,
  "mentions": [],
  "attachments": [],
  "reactions": []
},
{
  "id": "1377757032806879302",
  "author": {
    "id": 727302962917015612,
    "username": "ytrk",
    "display_name": "Still Bavarian"
  },
  "content": "the future of entertainment is ultra-personalized <:xita:1018997092611526706>",
  "timestamp": "2025-05-29T21:14:34.109000+00:00",
  "referenced_message": null,
  "mentions": [],
  "attachments": [],
  "reactions": []
},
{
  "id": "1377757413784162424",
  "author": {
    "id": 140880190158012417,
    "username": "cubox",
    "display_name": "Cubox"
  },
  "content": "i mean it's ultra personalized but shit",
  "timestamp": "2025-05-29T21:16:04.941000+00:00",
  "referenced_message": null,
  "mentions": [],
  "attachments": [],
  "reactions": []
},
{
  "id": "1377757816277827645",
  "author": {
    "id": 248184987193442314,
    "username": "magma._.",
    "display_name": "magam"
  },
  "content": "humans still have a bit of time left <:prayge:918020186706161674>",
  "timestamp": "2025-05-29T21:17:40.903000+00:00",
  "referenced_message": "1377754412004540517",
  "mentions": [],
  "attachments": [],
  "reactions": []
},
{
  "id": "1377758001469067394",
  "author": {
    "id": 727302962917015612,
    "username": "ytrk",
    "display_name": "Still Bavarian"
  },
  "content": "nah sonnet4 is pretty good",
  "timestamp": "2025-05-29T21:18:25.056000+00:00",
  "referenced_message": "1377757413784162424",
  "mentions": [],
  "attachments": [],
  "reactions": []
},
{
  "id": "1377758015037374464",
  "author": {
    "id": 140880190158012417,
    "username": "cubox",
    "display_name": "Cubox"
  },
  "content": "devstral forfeits",
  "timestamp": "2025-05-29T21:18:28.291000+00:00",
  "referenced_message": null,
  "mentions": [],
  "attachments": [
    "<attachment>"
  ],
  "reactions": []
},
{
  "id": "1377758079420076164",
  "author": {
    "id": 727302962917015612,
    "username": "ytrk",
    "display_name": "Still Bavarian"
  },
  "content": "I mean, for something made entirely by AI without human guidance that's excellent",
  "timestamp": "2025-05-29T21:18:43.641000+00:00",
  "referenced_message": null,
  "mentions": [],
  "attachments": [],
  "reactions": []
},
{
  "id": "1377758123141632111",
  "author": {
    "id": 727302962917015612,
    "username": "ytrk",
    "display_name": "Still Bavarian"
  },
  "content": "and much much faster than a human could do it",
  "timestamp": "2025-05-29T21:18:54.065000+00:00",
  "referenced_message": null,
  "mentions": [],
  "attachments": [],
  "reactions": []
},
{
  "id": "1377758149854888016",
  "author": {
    "id": 140880190158012417,
    "username": "cubox",
    "display_name": "Cubox"
  },
  "content": "new game by gemini2.5flash (refresh)",
  "timestamp": "2025-05-29T21:19:00.434000+00:00",
  "referenced_message": null,
  "mentions": [],
  "attachments": [],
  "reactions": []
},
{
  "id": "1377758375500054718",
  "author": {
    "id": 140880190158012417,
    "username": "cubox",
    "display_name": "Cubox"
  },
  "content": "pog",
  "timestamp": "2025-05-29T21:19:54.232000+00:00",
  "referenced_message": null,
  "mentions": [],
  "attachments": [],
  "reactions": []
},
{
  "id": "1377758426326765588",
  "author": {
    "id": 140880190158012417,
    "username": "cubox",
    "display_name": "Cubox"
  },
  "content": "oh sorry he wasn't done",
  "timestamp": "2025-05-29T21:20:06.350000+00:00",
  "referenced_message": "1377758149854888016",
  "mentions": [],
  "attachments": [],
  "reactions": []
},
{
  "id": "1377758992377581678",
  "author": {
    "id": 248184987193442314,
    "username": "magma._.",
    "display_name": "magam"
  },
  "content": "gray platforms disappear but still work <:rooVVV:981660256386052126>",
  "timestamp": "2025-05-29T21:22:21.307000+00:00",
  "referenced_message": "1377756938368057405",
  "mentions": [],
  "attachments": [],
  "reactions": []
},
{
  "id": "1377759643845005362",
  "author": {
    "id": 727302962917015612,
    "username": "ytrk",
    "display_name": "Still Bavarian"
  },
  "content": "you guys think in the future there will be personalized movies and TV shows?",
  "timestamp": "2025-05-29T21:24:56.629000+00:00",
  "referenced_message": null,
  "mentions": [],
  "attachments": [],
  "reactions": []
},
{
  "id": "1377759782815010947",
  "author": {
    "id": 727302962917015612,
    "username": "ytrk",
    "display_name": "Still Bavarian"
  },
  "content": "or at least, a massive increase in the amount of content available to watch. Even if AI is dogshit at generating stories, a human can write a storyboard much quicker and have it be animated by AI",
  "timestamp": "2025-05-29T21:25:29.762000+00:00",
  "referenced_message": null,
  "mentions": [],
  "attachments": [],
  "reactions": []
},
{
  "id": "1377759825999691836",
  "author": {
    "id": 248184987193442314,
    "username": "magma._.",
    "display_name": "magam"
  },
  "content": "hopefully not",
  "timestamp": "2025-05-29T21:25:40.058000+00:00",
  "referenced_message": null,
  "mentions": [],
  "attachments": [],
  "reactions": []
},
{
  "id": "1377759851190685756",
  "author": {
    "id": 727302962917015612,
    "username": "ytrk",
    "display_name": "Still Bavarian"
  },
  "content": "what's wrong with that?",
  "timestamp": "2025-05-29T21:25:46.064000+00:00",
  "referenced_message": null,
  "mentions": [],
  "attachments": [],
  "reactions": []
},
{
  "id": "1377759890747166832",
  "author": {
    "id": 727302962917015612,
    "username": "ytrk",
    "display_name": "Still Bavarian"
  },
  "content": "it's just like fan fictions, but now in video format",
  "timestamp": "2025-05-29T21:25:55.495000+00:00",
  "referenced_message": null,
  "mentions": [],
  "attachments": [],
  "reactions": []
},
{
  "id": "1377759896119803965",
  "author": {
    "id": 727302962917015612,
    "username": "ytrk",
    "display_name": "Still Bavarian"
  },
  "content": "<:rooVVV:981660256386052126>",
  "timestamp": "2025-05-29T21:25:56.776000+00:00",
  "referenced_message": null,
  "mentions": [],
  "attachments": [],
  "reactions": []
},
{
  "id": "1377759976683999243",
  "author": {
    "id": 727302962917015612,
    "username": "ytrk",
    "display_name": "Still Bavarian"
  },
  "content": "or actually youtube channels that can now do things that were formerly big-budget like special effects and having an (artifical) cast",
  "timestamp": "2025-05-29T21:26:15.984000+00:00",
  "referenced_message": null,
  "mentions": [],
  "attachments": [],
  "reactions": []
},
{
  "id": "1377760040613576736",
  "author": {
    "id": 248184987193442314,
    "username": "magma._.",
    "display_name": "magam"
  },
  "content": "📉 attention span\n📉 critical thinking\n📉 information transparency",
  "timestamp": "2025-05-29T21:26:31.226000+00:00",
  "referenced_message": null,
  "mentions": [],
  "attachments": [],
  "reactions": []
},
{
  "id": "1377760131541893212",
  "author": {
    "id": 727302962917015612,
    "username": "ytrk",
    "display_name": "Still Bavarian"
  },
  "content": "true. I do think that's problem with having everything AI generated. You can see what you want to see, which may not be factually correct",
  "timestamp": "2025-05-29T21:26:52.905000+00:00",
  "referenced_message": null,
  "mentions": [],
  "attachments": [],
  "reactions": []
},
{
  "id": "1377760195136192562",
  "author": {
    "id": 727302962917015612,
    "username": "ytrk",
    "display_name": "Still Bavarian"
  },
  "content": "we already have that happening with algorithms suggesting things that are either ragebait or that you agree with",
  "timestamp": "2025-05-29T21:27:08.067000+00:00",
  "referenced_message": null,
  "mentions": [],
  "attachments": [],
  "reactions": []
},
{
  "id": "1377760322068414465",
  "author": {
    "id": 248184987193442314,
    "username": "magma._.",
    "display_name": "magam"
  },
  "content": "📈 incentives to waste more time just staring at a screen without accomplishing anything",
  "timestamp": "2025-05-29T21:27:38.330000+00:00",
  "referenced_message": null,
  "mentions": [],
  "attachments": [],
  "reactions": []
},
{
  "id": "1377760781118083154",
  "author": {
    "id": 587899633456513036,
    "username": "saolapoggers",
    "display_name": "Ola"
  },
  "content": "<:peepocute:1263676783568621638> you know me",
  "timestamp": "2025-05-29T21:29:27.776000+00:00",
  "referenced_message": "1377760322068414465",
  "mentions": [],
  "attachments": [],
  "reactions": []
},
{
  "id": "1377760859568476300",
  "author": {
    "id": 142496872152432651,
    "username": "erinisafox",
    "display_name": "Audrey"
  },
  "content": "I wasn't expecting it to be so polished tbh",
  "timestamp": "2025-05-29T21:29:46.480000+00:00",
  "referenced_message": null,
  "mentions": [],
  "attachments": [],
  "reactions": []
},
{
  "id": "1377760903184908381",
  "author": {
    "id": 142496872152432651,
    "username": "erinisafox",
    "display_name": "Audrey"
  },
  "content": "The space jump one is fun <:pandanote:918001802333540362>",
  "timestamp": "2025-05-29T21:29:56.879000+00:00",
  "referenced_message": null,
  "mentions": [],
  "attachments": [],
  "reactions": []
},
{
  "id": "1377760964904091739",
  "author": {
    "id": 248184987193442314,
    "username": "magma._.",
    "display_name": "magam"
  },
  "content": "yea the graphics are cute",
  "timestamp": "2025-05-29T21:30:11.594000+00:00",
  "referenced_message": null,
  "mentions": [],
  "attachments": [],
  "reactions": [
    {
      "emoji": "<:roolove:918020195753279489>",
      "users": [
        {
          "id": 142496872152432651,
          "username": "erinisafox",
          "display_name": "Audrey"
        }
      ]
    }
  ]
},
{
  "id": "1377761379607515288",
  "author": {
    "id": 142496872152432651,
    "username": "erinisafox",
    "display_name": "Audrey"
  },
  "content": "Is it actually on the app store <:pandanote:918001802333540362>",
  "timestamp": "2025-05-29T21:31:50.467000+00:00",
  "referenced_message": null,
  "mentions": [],
  "attachments": [],
  "reactions": []
},
{
  "id": "1377761412486795295",
  "author": {
    "id": 142496872152432651,
    "username": "erinisafox",
    "display_name": "Audrey"
  },
  "content": "It asked if I want to download it",
  "timestamp": "2025-05-29T21:31:58.306000+00:00",
  "referenced_message": null,
  "mentions": [],
  "attachments": [],
  "reactions": []
},
{
  "id": "1377761464806539294",
  "author": {
    "id": 142496872152432651,
    "username": "erinisafox",
    "display_name": "Audrey"
  },
  "content": "Sonnet 3.7, anyway",
  "timestamp": "2025-05-29T21:32:10.780000+00:00",
  "referenced_message": null,
  "mentions": [],
  "attachments": [],
  "reactions": []
},
{
  "id": "1377761505445150880",
  "author": {
    "id": 142496872152432651,
    "username": "erinisafox",
    "display_name": "Audrey"
  },
  "content": "The others don't work too well on Android",
  "timestamp": "2025-05-29T21:32:20.469000+00:00",
  "referenced_message": null,
  "mentions": [],
  "attachments": [],
  "reactions": []
},
{
  "id": "1377761539263823913",
  "author": {
    "id": 142496872152432651,
    "username": "erinisafox",
    "display_name": "Audrey"
  },
  "content": "3.7 works flawlessly",
  "timestamp": "2025-05-29T21:32:28.532000+00:00",
  "referenced_message": null,
  "mentions": [],
  "attachments": [],
  "reactions": []
},
{
  "id": "1377762250974167162",
  "author": {
    "id": 727302962917015612,
    "username": "ytrk",
    "display_name": "Still Bavarian"
  },
  "content": "well hopefully we have less work to do in the future <:roolove:918020195753279489>",
  "timestamp": "2025-05-29T21:35:18.217000+00:00",
  "referenced_message": "1377760322068414465",
  "mentions": [],
  "attachments": [],
  "reactions": []
},
{
  "id": "1377762346008707073",
  "author": {
    "id": 727302962917015612,
    "username": "ytrk",
    "display_name": "Still Bavarian"
  },
  "content": "nah what's going to happen is that AI will be great at intellectual stuff but shitty at mining <:rooVVV:981660256386052126> \nwe will all work in the mines",
  "timestamp": "2025-05-29T21:35:40.875000+00:00",
  "referenced_message": null,
  "mentions": [],
  "attachments": [],
  "reactions": []
},
{
  "id": "1377762653535207555",
  "author": {
    "id": 140880190158012417,
    "username": "cubox",
    "display_name": "Cubox"
  },
  "content": "it's a \"phone web page app\" thing",
  "timestamp": "2025-05-29T21:36:54.195000+00:00",
  "referenced_message": "1377761412486795295",
  "mentions": [
    {
      "id": 142496872152432651,
      "username": "erinisafox",
      "display_name": "Audrey"
    }
  ],
  "attachments": [],
  "reactions": [
    {
      "emoji": "<:pandanote:918001802333540362>",
      "users": [
        {
          "id": 142496872152432651,
          "username": "erinisafox",
          "display_name": "Audrey"
        }
      ]
    }
  ]
},
{
  "id": "1377762958897057882",
  "author": {
    "id": 248184987193442314,
    "username": "magma._.",
    "display_name": "magam"
  },
  "content": "yes, and all the extra productivity will trickle down to those who would've done those jobs <:pandaww:918019879456636978>",
  "timestamp": "2025-05-29T21:38:06.999000+00:00",
  "referenced_message": "1377762250974167162",
  "mentions": [],
  "attachments": [],
  "reactions": []
},
{
  "id": "1377763339043737774",
  "author": {
    "id": 142496872152432651,
    "username": "erinisafox",
    "display_name": "Audrey"
  },
  "content": "Hopefully it creates a world where everyone has a higher standard of living and not just make a few people rich while everyone else gets unemployed <:bonk:954485324975833159>",
  "timestamp": "2025-05-29T21:39:37.633000+00:00",
  "referenced_message": null,
  "mentions": [],
  "attachments": [],
  "reactions": []
},
{
  "id": "1377763488553894069",
  "author": {
    "id": 727302962917015612,
    "username": "ytrk",
    "display_name": "Still Bavarian"
  },
  "content": "it's probably going to be a bit of both, imo <:isee:918001433419333682>",
  "timestamp": "2025-05-29T21:40:13.279000+00:00",
  "referenced_message": null,
  "mentions": [],
  "attachments": [],
  "reactions": []
},
{
  "id": "1377763551581831372",
  "author": {
    "id": 727302962917015612,
    "username": "ytrk",
    "display_name": "Still Bavarian"
  },
  "content": "I mean, labor of the best will probably increase in value",
  "timestamp": "2025-05-29T21:40:28.306000+00:00",
  "referenced_message": null,
  "mentions": [],
  "attachments": [],
  "reactions": []
},
{
  "id": "1377763629742686279",
  "author": {
    "id": 727302962917015612,
    "username": "ytrk",
    "display_name": "Still Bavarian"
  },
  "content": "if we consider AI to be a 2x multiplier on productivity, then the difference between the worst and the best will grow significantly and thus income inequality will rise",
  "timestamp": "2025-05-29T21:40:46.941000+00:00",
  "referenced_message": null,
  "mentions": [],
  "attachments": [],
  "reactions": []
},
{
  "id": "1377763967773966406",
  "author": {
    "id": 142496872152432651,
    "username": "erinisafox",
    "display_name": "Audrey"
  },
  "content": "We need some LLMs to generate personalized LLMs for everyone",
  "timestamp": "2025-05-29T21:42:07.534000+00:00",
  "referenced_message": null,
  "mentions": [],
  "attachments": [],
  "reactions": []
},
{
  "id": "1377764082916261938",
  "author": {
    "id": 140880190158012417,
    "username": "cubox",
    "display_name": "Cubox"
  },
  "content": "that's called fine tuning",
  "timestamp": "2025-05-29T21:42:34.986000+00:00",
  "referenced_message": null,
  "mentions": [],
  "attachments": [],
  "reactions": [
    {
      "emoji": "<:pandanote:918001802333540362>",
      "users": [
        {
          "id": 142496872152432651,
          "username": "erinisafox",
          "display_name": "Audrey"
        }
      ]
    }
  ]
},
{
  "id": "1377764094286889101",
  "author": {
    "id": 140880190158012417,
    "username": "cubox",
    "display_name": "Cubox"
  },
  "content": "it already exists",
  "timestamp": "2025-05-29T21:42:37.697000+00:00",
  "referenced_message": null,
  "mentions": [],
  "attachments": [],
  "reactions": []
},
{
  "id": "1377764164256403637",
  "author": {
    "id": 140880190158012417,
    "username": "cubox",
    "display_name": "Cubox"
  },
  "content": "devstral is COOKING",
  "timestamp": "2025-05-29T21:42:54.379000+00:00",
  "referenced_message": null,
  "mentions": [],
  "attachments": [],
  "reactions": []
},
{
  "id": "1377764376198516788",
  "author": {
    "id": 727302962917015612,
    "username": "ytrk",
    "display_name": "Still Bavarian"
  },
  "content": "you think we'll get AGI?",
  "timestamp": "2025-05-29T21:43:44.910000+00:00",
  "referenced_message": null,
  "mentions": [],
  "attachments": [],
  "reactions": []
},
{
  "id": "1377764394548592771",
  "author": {
    "id": 727302962917015612,
    "username": "ytrk",
    "display_name": "Still Bavarian"
  },
  "content": "<:xita:1018997092611526706>",
  "timestamp": "2025-05-29T21:43:49.285000+00:00",
  "referenced_message": null,
  "mentions": [],
  "attachments": [],
  "reactions": []
},
{
  "id": "1377764433136189552",
  "author": {
    "id": 727302962917015612,
    "username": "ytrk",
    "display_name": "Still Bavarian"
  },
  "content": "I say it's possible within our lifetimes",
  "timestamp": "2025-05-29T21:43:58.485000+00:00",
  "referenced_message": null,
  "mentions": [],
  "attachments": [],
  "reactions": []
},
{
  "id": "1377764596064190644",
  "author": {
    "id": 727302962917015612,
    "username": "ytrk",
    "display_name": "Still Bavarian"
  },
  "content": "human brains are kind of cracked. They run on a mere 20W of power",
  "timestamp": "2025-05-29T21:44:37.330000+00:00",
  "referenced_message": null,
  "mentions": [],
  "attachments": [],
  "reactions": []
},
{
  "id": "1377764841640689796",
  "author": {
    "id": 140880190158012417,
    "username": "cubox",
    "display_name": "Cubox"
  },
  "content": "i think devstral is stuck in a loop",
  "timestamp": "2025-05-29T21:45:35.880000+00:00",
  "referenced_message": null,
  "mentions": [],
  "attachments": [],
  "reactions": []
},
{
  "id": "1377770738492248074",
  "author": {
    "id": 612660205913899029,
    "username": "mansimum",
    "display_name": "Väinämöinen"
  },
  "content": "wanna play some time again? <:xita:1018997092611526706> <:poggies:918020176849543198>",
  "timestamp": "2025-05-29T22:09:01.799000+00:00",
  "referenced_message": "1377758375500054718",
  "mentions": [
    {
      "id": 140880190158012417,
      "username": "cubox",
      "display_name": "Cubox"
    }
  ],
  "attachments": [],
  "reactions": []
},
{
  "id": "1377771266714374154",
  "author": {
    "id": 140880190158012417,
    "username": "cubox",
    "display_name": "Cubox"
  },
  "content": "not sv itself no, but another game",
  "timestamp": "2025-05-29T22:11:07.737000+00:00",
  "referenced_message": "1377770738492248074",
  "mentions": [
    {
      "id": 612660205913899029,
      "username": "mansimum",
      "display_name": "Väinämöinen"
    }
  ],
  "attachments": [],
  "reactions": [
    {
      "emoji": "<:prayge:918020186706161674>",
      "users": [
        {
          "id": 612660205913899029,
          "username": "mansimum",
          "display_name": "Väinämöinen"
        }
      ]
    }
  ]
},
{
  "id": "1377771747067039775",
  "author": {
    "id": 140880190158012417,
    "username": "cubox",
    "display_name": "Cubox"
  },
  "content": "devstral uploaded",
  "timestamp": "2025-05-29T22:13:02.262000+00:00",
  "referenced_message": null,
  "mentions": [],
  "attachments": [],
  "reactions": []
},
{
  "id": "1377772979928432721",
  "author": {
    "id": 142496872152432651,
    "username": "erinisafox",
    "display_name": "Audrey"
  },
  "content": "22779, do I have the world record in Cosmic Jump",
  "timestamp": "2025-05-29T22:17:56.199000+00:00",
  "referenced_message": null,
  "mentions": [],
  "attachments": [],
  "reactions": []
},
{
  "id": "1377774264803131454",
  "author": {
    "id": 248184987193442314,
    "username": "magma._.",
    "display_name": "magam"
  },
  "content": "feels like life",
  "timestamp": "2025-05-29T22:23:02.537000+00:00",
  "referenced_message": "1377771747067039775",
  "mentions": [],
  "attachments": [],
  "reactions": []
},
{
  "id": "1377774868182863926",
  "author": {
    "id": 140880190158012417,
    "username": "cubox",
    "display_name": "Cubox"
  },
  "content": "which one is that",
  "timestamp": "2025-05-29T22:25:26.394000+00:00",
  "referenced_message": "1377772979928432721",
  "mentions": [
    {
      "id": 142496872152432651,
      "username": "erinisafox",
      "display_name": "Audrey"
    }
  ],
  "attachments": [],
  "reactions": []
},
{
  "id": "1377774915704459275",
  "author": {
    "id": 140880190158012417,
    "username": "cubox",
    "display_name": "Cubox"
  },
  "content": "3.7?",
  "timestamp": "2025-05-29T22:25:37.724000+00:00",
  "referenced_message": null,
  "mentions": [],
  "attachments": [],
  "reactions": [
    {
      "emoji": "✅",
      "users": [
        {
          "id": 142496872152432651,
          "username": "erinisafox",
          "display_name": "Audrey"
        }
      ]
    }
  ]
},
{
  "id": "1377775443096371341",
  "author": {
    "id": 140880190158012417,
    "username": "cubox",
    "display_name": "Cubox"
  },
  "content": "https://doodlejump24.io/\n\nthis is the original for reference",
  "timestamp": "2025-05-29T22:27:43.464000+00:00",
  "referenced_message": null,
  "mentions": [],
  "attachments": [],
  "reactions": []
},
{
  "id": "1377775514332430409",
  "author": {
    "id": 140880190158012417,
    "username": "cubox",
    "display_name": "Cubox"
  },
  "content": "i'm gonna take the best one of the games it made and get it to improve it",
  "timestamp": "2025-05-29T22:28:00.448000+00:00",
  "referenced_message": null,
  "mentions": [],
  "attachments": [],
  "reactions": []
},
{
  "id": "1377775772743368824",
  "author": {
    "id": 140880190158012417,
    "username": "cubox",
    "display_name": "Cubox"
  },
  "content": "new contender :https://cubox.dev/files/jump/r1/",
  "timestamp": "2025-05-29T22:29:02.058000+00:00",
  "referenced_message": null,
  "mentions": [],
  "attachments": [],
  "reactions": []
},
{
  "id": "1377776109181210744",
  "author": {
    "id": 140880190158012417,
    "username": "cubox",
    "display_name": "Cubox"
  },
  "content": "i like sonnet4 better",
  "timestamp": "2025-05-29T22:30:22.271000+00:00",
  "referenced_message": null,
  "mentions": [],
  "attachments": [],
  "reactions": []
},
{
  "id": "1377777241634635779",
  "author": {
    "id": 142496872152432651,
    "username": "erinisafox",
    "display_name": "Audrey"
  },
  "content": "Doodle Jump is a childhood memory <:pandanote:918001802333540362>",
  "timestamp": "2025-05-29T22:34:52.269000+00:00",
  "referenced_message": null,
  "mentions": [],
  "attachments": [],
  "reactions": [
    {
      "emoji": "<:voteyes:918020266410520587>",
      "users": [
        {
          "id": 1089928222252208170,
          "username": "autumnprincess",
          "display_name": "kanthru"
        },
        {
          "id": 612660205913899029,
          "username": "mansimum",
          "display_name": "Väinämöinen"
        },
        {
          "id": 362198518129098752,
          "username": "l_o_m",
          "display_name": "lom"
        },
        {
          "id": 248184987193442314,
          "username": "magma._.",
          "display_name": "magam"
        }
      ]
    }
  ]
},
{
  "id": "1377779069013327932",
  "author": {
    "id": 140880190158012417,
    "username": "cubox",
    "display_name": "Cubox"
  },
  "content": "yep",
  "timestamp": "2025-05-29T22:42:07.950000+00:00",
  "referenced_message": null,
  "mentions": [],
  "attachments": [],
  "reactions": []
},
{
  "id": "1377782502726566040",
  "author": {
    "id": 362198518129098752,
    "username": "l_o_m",
    "display_name": "lom"
  },
  "content": "wtf doodle jump transcends generations",
  "timestamp": "2025-05-29T22:55:46.611000+00:00",
  "referenced_message": null,
  "mentions": [],
  "attachments": [],
  "reactions": []
},
{
  "id": "1377782516236156989",
  "author": {
    "id": 362198518129098752,
    "username": "l_o_m",
    "display_name": "lom"
  },
  "content": "because same for me",
  "timestamp": "2025-05-29T22:55:49.832000+00:00",
  "referenced_message": null,
  "mentions": [],
  "attachments": [],
  "reactions": [
    {
      "emoji": "<:pandanote:918001802333540362>",
      "users": [
        {
          "id": 142496872152432651,
          "username": "erinisafox",
          "display_name": "Audrey"
        }
      ]
    }
  ]
},
{
  "id": "1377784492613308536",
  "author": {
    "id": 142496872152432651,
    "username": "erinisafox",
    "display_name": "Audrey"
  },
  "content": "Flappy Bird 🐦",
  "timestamp": "2025-05-29T23:03:41.037000+00:00",
  "referenced_message": null,
  "mentions": [],
  "attachments": [],
  "reactions": []
},
{
  "id": "1377784544392122429",
  "author": {
    "id": 142496872152432651,
    "username": "erinisafox",
    "display_name": "Audrey"
  },
  "content": "<@489569435812757519> wasn't the main character based on you",
  "timestamp": "2025-05-29T23:03:53.382000+00:00",
  "referenced_message": null,
  "mentions": [
    {
      "id": 489569435812757519,
      "username": "dread2much",
      "display_name": "dread"
    }
  ],
  "attachments": [],
  "reactions": []
},
{
  "id": "1378094467927642132",
  "author": {
    "id": 140880190158012417,
    "username": "cubox",
    "display_name": "Cubox"
  },
  "content": "<@362198518129098752> tham was using an old project from school that's about making fractals because she wants to video it for her website to show it, but it was quite slow on her laptop. And after checking it was single threaded and one CPU was going to the max.\n\nI said why not ask copilot to add multithreading. So with Claude sonnet 4 and a single prompt it managed to implement it and make it work flawlessly with extra features (an option to toggle it on and off) in like 5 minutes and now her code is so much faster",
  "timestamp": "2025-05-30T19:35:24.911000+00:00",
  "referenced_message": null,
  "mentions": [
    {
      "id": 362198518129098752,
      "username": "l_o_m",
      "display_name": "lom"
    }
  ],
  "attachments": [],
  "reactions": []
},
{
  "id": "1378098295112601660",
  "author": {
    "id": 362198518129098752,
    "username": "l_o_m",
    "display_name": "lom"
  },
  "content": "ban",
  "timestamp": "2025-05-30T19:50:37.383000+00:00",
  "referenced_message": null,
  "mentions": [],
  "attachments": [],
  "reactions": []
},
{
  "id": "1378098958513078292",
  "author": {
    "id": 140880190158012417,
    "username": "cubox",
    "display_name": "Cubox"
  },
  "content": "What do you mean ban",
  "timestamp": "2025-05-30T19:53:15.550000+00:00",
  "referenced_message": null,
  "mentions": [],
  "attachments": [],
  "reactions": []
},
{
  "id": "1378099558717984788",
  "author": {
    "id": 362198518129098752,
    "username": "l_o_m",
    "display_name": "lom"
  },
  "content": "ban claude",
  "timestamp": "2025-05-30T19:55:38.650000+00:00",
  "referenced_message": null,
  "mentions": [],
  "attachments": [],
  "reactions": []
},
{
  "id": "1378099585557331988",
  "author": {
    "id": 362198518129098752,
    "username": "l_o_m",
    "display_name": "lom"
  },
  "content": "too OP",
  "timestamp": "2025-05-30T19:55:45.049000+00:00",
  "referenced_message": null,
  "mentions": [],
  "attachments": [],
  "reactions": []
},
{
  "id": "1378100287964971270",
  "author": {
    "id": 140880190158012417,
    "username": "cubox",
    "display_name": "Cubox"
  },
  "content": "Bro's banning the industrial revolution",
  "timestamp": "2025-05-30T19:58:32.516000+00:00",
  "referenced_message": null,
  "mentions": [],
  "attachments": [],
  "reactions": []
},
{
  "id": "1378144864343949482",
  "author": {
    "id": 529303103015485441,
    "username": "thamle",
    "display_name": "Math"
  },
  "content": "It's not \"after checking\" that I know it's single thread. I made it single thread because it's forbidden multi-thread",
  "timestamp": "2025-05-30T22:55:40.353000+00:00",
  "referenced_message": "1378094467927642132",
  "mentions": [
    {
      "id": 140880190158012417,
      "username": "cubox",
      "display_name": "Cubox"
    }
  ],
  "attachments": [],
  "reactions": []
},
{
  "id": "1378144958762188830",
  "author": {
    "id": 529303103015485441,
    "username": "thamle",
    "display_name": "Math"
  },
  "content": "And not flawless cause data race",
  "timestamp": "2025-05-30T22:56:02.864000+00:00",
  "referenced_message": null,
  "mentions": [],
  "attachments": [],
  "reactions": []
},
{
  "id": "1378194447929446500",
  "author": {
    "id": 140880190158012417,
    "username": "cubox",
    "display_name": "Cubox"
  },
  "content": "Oh that's true",
  "timestamp": "2025-05-31T02:12:42.001000+00:00",
  "referenced_message": "1378144864343949482",
  "mentions": [
    {
      "id": 529303103015485441,
      "username": "thamle",
      "display_name": "Math"
    }
  ],
  "attachments": [],
  "reactions": []
},
{
  "id": "1378194461875769394",
  "author": {
    "id": 140880190158012417,
    "username": "cubox",
    "display_name": "Cubox"
  },
  "content": "I forgot that bit",
  "timestamp": "2025-05-31T02:12:45.326000+00:00",
  "referenced_message": null,
  "mentions": [],
  "attachments": [],
  "reactions": []
},
{
  "id": "1378194577877631048",
  "author": {
    "id": 140880190158012417,
    "username": "cubox",
    "display_name": "Cubox"
  },
  "content": "Poor gpt4.1",
  "timestamp": "2025-05-31T02:13:12.983000+00:00",
  "referenced_message": "1378144958762188830",
  "mentions": [
    {
      "id": 529303103015485441,
      "username": "thamle",
      "display_name": "Math"
    }
  ],
  "attachments": [],
  "reactions": []
},
{
  "id": "1379221533880488045",
  "author": {
    "id": 140880190158012417,
    "username": "cubox",
    "display_name": "Cubox"
  },
  "content": "r1 is now working on the doodle jump",
  "timestamp": "2025-06-02T22:13:58.374000+00:00",
  "referenced_message": null,
  "mentions": [],
  "attachments": [],
  "reactions": []
},
{
  "id": "1379223603957989458",
  "author": {
    "id": 140880190158012417,
    "username": "cubox",
    "display_name": "Cubox"
  },
  "content": "https://cubox.dev/files/jump/r1/",
  "timestamp": "2025-06-02T22:22:11.919000+00:00",
  "referenced_message": null,
  "mentions": [],
  "attachments": [],
  "reactions": [
    {
      "emoji": "<:pandanote:918001802333540362>",
      "users": [
        {
          "id": 142496872152432651,
          "username": "erinisafox",
          "display_name": "Audrey"
        }
      ]
    }
  ]
},
{
  "id": "1379227039524982844",
  "author": {
    "id": 727302962917015612,
    "username": "ytrk",
    "display_name": "Still Bavarian"
  },
  "content": "how do I jump",
  "timestamp": "2025-06-02T22:35:51.022000+00:00",
  "referenced_message": null,
  "mentions": [],
  "attachments": [],
  "reactions": []
},
{
  "id": "1379227060337377350",
  "author": {
    "id": 727302962917015612,
    "username": "ytrk",
    "display_name": "Still Bavarian"
  },
  "content": "cosmic jump was the best",
  "timestamp": "2025-06-02T22:35:55.984000+00:00",
  "referenced_message": null,
  "mentions": [],
  "attachments": [],
  "reactions": [
    {
      "emoji": "✅",
      "users": [
        {
          "id": 142496872152432651,
          "username": "erinisafox",
          "display_name": "Audrey"
        }
      ]
    }
  ]
},
{
  "id": "1379229215647334563",
  "author": {
    "id": 142496872152432651,
    "username": "erinisafox",
    "display_name": "Audrey"
  },
  "content": "Cosmic Leap shakes the screen every time you bounce <:pandanote:918001802333540362>",
  "timestamp": "2025-06-02T22:44:29.850000+00:00",
  "referenced_message": null,
  "mentions": [],
  "attachments": [],
  "reactions": []
},
{
  "id": "1379229236019073094",
  "author": {
    "id": 489569435812757519,
    "username": "dread2much",
    "display_name": "dread"
  },
  "content": "Why don’t you jump off my nuts",
  "timestamp": "2025-06-02T22:44:34.707000+00:00",
  "referenced_message": null,
  "mentions": [],
  "attachments": [],
  "reactions": [
    {
      "emoji": "<:xita:1018997092611526706>",
      "users": [
        {
          "id": 142496872152432651,
          "username": "erinisafox",
          "display_name": "Audrey"
        }
      ]
    }
  ]
},
{
  "id": "1379229304264593459",
  "author": {
    "id": 489569435812757519,
    "username": "dread2much",
    "display_name": "dread"
  },
  "content": "Keep bouncing you filthy whore",
  "timestamp": "2025-06-02T22:44:50.978000+00:00",
  "referenced_message": "1379229215647334563",
  "mentions": [
    {
      "id": 142496872152432651,
      "username": "erinisafox",
      "display_name": "Audrey"
    }
  ],
  "attachments": [],
  "reactions": [
    {
      "emoji": "<:pandanote:918001802333540362>",
      "users": [
        {
          "id": 142496872152432651,
          "username": "erinisafox",
          "display_name": "Audrey"
        }
      ]
    }
  ]
},
{
  "id": "1379229350867505252",
  "author": {
    "id": 142496872152432651,
    "username": "erinisafox",
    "display_name": "Audrey"
  },
  "content": "How's the internship going",
  "timestamp": "2025-06-02T22:45:02.089000+00:00",
  "referenced_message": null,
  "mentions": [],
  "attachments": [],
  "reactions": []
},
{
  "id": "1379229495730634915",
  "author": {
    "id": 489569435812757519,
    "username": "dread2much",
    "display_name": "dread"
  },
  "content": "Kinda pissed pff because I lost the whole second half of my day to meeting people and some presentation that was randomly thrown on my calendar",
  "timestamp": "2025-06-02T22:45:36.627000+00:00",
  "referenced_message": null,
  "mentions": [],
  "attachments": [],
  "reactions": [
    {
      "emoji": "<:xita:1018997092611526706>",
      "users": [
        {
          "id": 142496872152432651,
          "username": "erinisafox",
          "display_name": "Audrey"
        }
      ]
    }
  ]
},
{
  "id": "1379229505947701331",
  "author": {
    "id": 142496872152432651,
    "username": "erinisafox",
    "display_name": "Audrey"
  },
  "content": "Bro I actually have a woman build now, lom knows",
  "timestamp": "2025-06-02T22:45:39.063000+00:00",
  "referenced_message": "1379229304264593459",
  "mentions": [],
  "attachments": [],
  "reactions": []
},
{
  "id": "1379229550407454872",
  "author": {
    "id": 489569435812757519,
    "username": "dread2much",
    "display_name": "dread"
  },
  "content": "I have to learn this huge multi body dynamics program within a few days",
  "timestamp": "2025-06-02T22:45:49.663000+00:00",
  "referenced_message": null,
  "mentions": [],
  "attachments": [],
  "reactions": [
    {
      "emoji": "<:pandanote:918001802333540362>",
      "users": [
        {
          "id": 142496872152432651,
          "username": "erinisafox",
          "display_name": "Audrey"
        }
      ]
    }
  ]
},
{
  "id": "1379229608372604969",
  "author": {
    "id": 489569435812757519,
    "username": "dread2much",
    "display_name": "dread"
  },
  "content": "Basically have like over 1000 pages worth of material to go through and practice",
  "timestamp": "2025-06-02T22:46:03.483000+00:00",
  "referenced_message": null,
  "mentions": [],
  "attachments": [],
  "reactions": []
},
{
  "id": "1379229648839245874",
  "author": {
    "id": 489569435812757519,
    "username": "dread2much",
    "display_name": "dread"
  },
  "content": "And I can’t afford to just lose half a day",
  "timestamp": "2025-06-02T22:46:13.131000+00:00",
  "referenced_message": null,
  "mentions": [],
  "attachments": [],
  "reactions": []
},
{
  "id": "1379229709920895027",
  "author": {
    "id": 142496872152432651,
    "username": "erinisafox",
    "display_name": "Audrey"
  },
  "content": "Imagine being an intern and having more work than me",
  "timestamp": "2025-06-02T22:46:27.694000+00:00",
  "referenced_message": null,
  "mentions": [],
  "attachments": [],
  "reactions": []
},
{
  "id": "1379229723028094997",
  "author": {
    "id": 142496872152432651,
    "username": "erinisafox",
    "display_name": "Audrey"
  },
  "content": "🫂",
  "timestamp": "2025-06-02T22:46:30.819000+00:00",
  "referenced_message": null,
  "mentions": [],
  "attachments": [],
  "reactions": []
},
{
  "id": "1379229765873172631",
  "author": {
    "id": 489569435812757519,
    "username": "dread2much",
    "display_name": "dread"
  },
  "content": "My boss is a PhD in mechanical engineering",
  "timestamp": "2025-06-02T22:46:41.034000+00:00",
  "referenced_message": null,
  "mentions": [],
  "attachments": [],
  "reactions": [
    {
      "emoji": "<:pandanote:918001802333540362>",
      "users": [
        {
          "id": 142496872152432651,
          "username": "erinisafox",
          "display_name": "Audrey"
        }
      ]
    }
  ]
},
{
  "id": "1379229782188888125",
  "author": {
    "id": 489569435812757519,
    "username": "dread2much",
    "display_name": "dread"
  },
  "content": "Fucking genius",
  "timestamp": "2025-06-02T22:46:44.924000+00:00",
  "referenced_message": null,
  "mentions": [],
  "attachments": [],
  "reactions": []
},
{
  "id": "1379229828745658450",
  "author": {
    "id": 489569435812757519,
    "username": "dread2much",
    "display_name": "dread"
  },
  "content": "He’s literally cracked in every possible way",
  "timestamp": "2025-06-02T22:46:56.024000+00:00",
  "referenced_message": null,
  "mentions": [],
  "attachments": [],
  "reactions": []
},
{
  "id": "1379229838728106016",
  "author": {
    "id": 142496872152432651,
    "username": "erinisafox",
    "display_name": "Audrey"
  },
  "content": "Oh then you're fine, he knows how slow progress goes",
  "timestamp": "2025-06-02T22:46:58.404000+00:00",
  "referenced_message": null,
  "mentions": [],
  "attachments": [],
  "reactions": []
},
{
  "id": "1379229874916687983",
  "author": {
    "id": 142496872152432651,
    "username": "erinisafox",
    "display_name": "Audrey"
  },
  "content": "He won't be mad if you take a while",
  "timestamp": "2025-06-02T22:47:07.032000+00:00",
  "referenced_message": null,
  "mentions": [],
  "attachments": [],
  "reactions": []
},
{
  "id": "1379229911750803507",
  "author": {
    "id": 489569435812757519,
    "username": "dread2much",
    "display_name": "dread"
  },
  "content": "Yeah but I want to get this shit done",
  "timestamp": "2025-06-02T22:47:15.814000+00:00",
  "referenced_message": null,
  "mentions": [],
  "attachments": [],
  "reactions": []
},
{
  "id": "1379229973164064889",
  "author": {
    "id": 489569435812757519,
    "username": "dread2much",
    "display_name": "dread"
  },
  "content": "It’s hard enough to do, even if I have no distractions",
  "timestamp": "2025-06-02T22:47:30.456000+00:00",
  "referenced_message": null,
  "mentions": [],
  "attachments": [],
  "reactions": []
},
{
  "id": "1379229998078230590",
  "author": {
    "id": 489569435812757519,
    "username": "dread2much",
    "display_name": "dread"
  },
  "content": "Hard to focus",
  "timestamp": "2025-06-02T22:47:36.396000+00:00",
  "referenced_message": null,
  "mentions": [],
  "attachments": [],
  "reactions": []
},
{
  "id": "1379230093901299784",
  "author": {
    "id": 1089928222252208170,
    "username": "autumnprincess",
    "display_name": "kanthru"
  },
  "content": "https://tenor.com/view/cirno-cirno-fumo-fumo-touhou-fumo-plush-fumo-gif-25572816",
  "timestamp": "2025-06-02T22:47:59.242000+00:00",
  "referenced_message": null,
  "mentions": [],
  "attachments": [],
  "reactions": [
    {
      "emoji": "<:pandanote:918001802333540362>",
      "users": [
        {
          "id": 142496872152432651,
          "username": "erinisafox",
          "display_name": "Audrey"
        }
      ]
    }
  ]
},
{
  "id": "1379230125886935092",
  "author": {
    "id": 142496872152432651,
    "username": "erinisafox",
    "display_name": "Audrey"
  },
  "content": "Today is a big day for me. Took a month, but finally sent out a 70 page document. So now I don't look like a chump to my boss because I met a deliverable <:pandanote:918001802333540362>",
  "timestamp": "2025-06-02T22:48:06.868000+00:00",
  "referenced_message": "1379229911750803507",
  "mentions": [],
  "attachments": [],
  "reactions": []
},
{
  "id": "1379230128521085132",
  "author": {
    "id": 489569435812757519,
    "username": "dread2much",
    "display_name": "dread"
  },
  "content": "I wish I could just sit at my desk for 8 hours",
  "timestamp": "2025-06-02T22:48:07.496000+00:00",
  "referenced_message": null,
  "mentions": [],
  "attachments": [],
  "reactions": []
},
{
  "id": "1379230153246380232",
  "author": {
    "id": 489569435812757519,
    "username": "dread2much",
    "display_name": "dread"
  },
  "content": "Instead of having other things to do",
  "timestamp": "2025-06-02T22:48:13.391000+00:00",
  "referenced_message": null,
  "mentions": [],
  "attachments": [],
  "reactions": []
},
{
  "id": "1379230250801692802",
  "author": {
    "id": 142496872152432651,
    "username": "erinisafox",
    "display_name": "Audrey"
  },
  "content": "Welcome to life <:KEKW:918001534376230922>",
  "timestamp": "2025-06-02T22:48:36.650000+00:00",
  "referenced_message": "1379230153246380232",
  "mentions": [],
  "attachments": [],
  "reactions": []
},
{
  "id": "1379230287690727474",
  "author": {
    "id": 489569435812757519,
    "username": "dread2much",
    "display_name": "dread"
  },
  "content": "Tell me about it",
  "timestamp": "2025-06-02T22:48:45.445000+00:00",
  "referenced_message": null,
  "mentions": [],
  "attachments": [],
  "reactions": []
},
{
  "id": "1379230293763952680",
  "author": {
    "id": 142496872152432651,
    "username": "erinisafox",
    "display_name": "Audrey"
  },
  "content": "There's always some random thing you have to do",
  "timestamp": "2025-06-02T22:48:46.893000+00:00",
  "referenced_message": null,
  "mentions": [],
  "attachments": [],
  "reactions": []
},
{
  "id": "1379230326328524863",
  "author": {
    "id": 489569435812757519,
    "username": "dread2much",
    "display_name": "dread"
  },
  "content": "I just spent an hour cooking and eating",
  "timestamp": "2025-06-02T22:48:54.657000+00:00",
  "referenced_message": null,
  "mentions": [],
  "attachments": [],
  "reactions": []
},
{
  "id": "1379230360021242061",
  "author": {
    "id": 489569435812757519,
    "username": "dread2much",
    "display_name": "dread"
  },
  "content": "I also have to make lunch for tomorrow",
  "timestamp": "2025-06-02T22:49:02.690000+00:00",
  "referenced_message": null,
  "mentions": [],
  "attachments": [],
  "reactions": []
},
{
  "id": "1379230402274791474",
  "author": {
    "id": 489569435812757519,
    "username": "dread2much",
    "display_name": "dread"
  },
  "content": "Bed is in 3.5 hours",
  "timestamp": "2025-06-02T22:49:12.764000+00:00",
  "referenced_message": null,
  "mentions": [],
  "attachments": [],
  "reactions": [
    {
      "emoji": "<:xita:1018997092611526706>",
      "users": [
        {
          "id": 142496872152432651,
          "username": "erinisafox",
          "display_name": "Audrey"
        }
      ]
    }
  ]
},
{
  "id": "1379230459480903852",
  "author": {
    "id": 489569435812757519,
    "username": "dread2much",
    "display_name": "dread"
  },
  "content": "I haven’t done anything other than work commute store cook",
  "timestamp": "2025-06-02T22:49:26.403000+00:00",
  "referenced_message": null,
  "mentions": [],
  "attachments": [],
  "reactions": []
},
{
  "id": "1379230472193704078",
  "author": {
    "id": 142496872152432651,
    "username": "erinisafox",
    "display_name": "Audrey"
  },
  "content": "After some position level you stop having just one thing to do and have to start juggling and pushing back deadlines",
  "timestamp": "2025-06-02T22:49:29.434000+00:00",
  "referenced_message": null,
  "mentions": [],
  "attachments": [],
  "reactions": []
},
{
  "id": "1379230576254386236",
  "author": {
    "id": 142496872152432651,
    "username": "erinisafox",
    "display_name": "Audrey"
  },
  "content": "I envy the guys in my team who can focus on one task 😭",
  "timestamp": "2025-06-02T22:49:54.244000+00:00",
  "referenced_message": null,
  "mentions": [],
  "attachments": [],
  "reactions": []
},
{
  "id": "1379230656373981245",
  "author": {
    "id": 142496872152432651,
    "username": "erinisafox",
    "display_name": "Audrey"
  },
  "content": "Yeah that's my life now <:KEKW:918001534376230922>",
  "timestamp": "2025-06-02T22:50:13.346000+00:00",
  "referenced_message": "1379230459480903852",
  "mentions": [],
  "attachments": [],
  "reactions": []
},
{
  "id": "1379230768869539960",
  "author": {
    "id": 142496872152432651,
    "username": "erinisafox",
    "display_name": "Audrey"
  },
  "content": "It's super gay",
  "timestamp": "2025-06-02T22:50:40.167000+00:00",
  "referenced_message": null,
  "mentions": [],
  "attachments": [],
  "reactions": []
},
{
  "id": "1379230884426682468",
  "author": {
    "id": 140880190158012417,
    "username": "cubox",
    "display_name": "Cubox"
  },
  "content": "if you knew what PhD is an inside joke for in here",
  "timestamp": "2025-06-02T22:51:07.718000+00:00",
  "referenced_message": "1379230093901299784",
  "mentions": [
    {
      "id": 1089928222252208170,
      "username": "autumnprincess",
      "display_name": "kanthru"
    }
  ],
  "attachments": [],
  "reactions": [
    {
      "emoji": "<:xita:1018997092611526706>",
      "users": [
        {
          "id": 142496872152432651,
          "username": "erinisafox",
          "display_name": "Audrey"
        }
      ]
    }
  ]
},
{
  "id": "1379231382638694481",
  "author": {
    "id": 489569435812757519,
    "username": "dread2much",
    "display_name": "dread"
  },
  "content": "yeah",
  "timestamp": "2025-06-02T22:53:06.501000+00:00",
  "referenced_message": "1379230768869539960",
  "mentions": [
    {
      "id": 142496872152432651,
      "username": "erinisafox",
      "display_name": "Audrey"
    }
  ],
  "attachments": [],
  "reactions": []
},
{
  "id": "1379231396886745118",
  "author": {
    "id": 489569435812757519,
    "username": "dread2much",
    "display_name": "dread"
  },
  "content": "its really just getting pegged",
  "timestamp": "2025-06-02T22:53:09.898000+00:00",
  "referenced_message": null,
  "mentions": [],
  "attachments": [],
  "reactions": []
},
{
  "id": "1379231423113723914",
  "author": {
    "id": 489569435812757519,
    "username": "dread2much",
    "display_name": "dread"
  },
  "content": "which is super fucking gay",
  "timestamp": "2025-06-02T22:53:16.151000+00:00",
  "referenced_message": null,
  "mentions": [],
  "attachments": [],
  "reactions": []
},
{
  "id": "1379231494333005885",
  "author": {
    "id": 489569435812757519,
    "username": "dread2much",
    "display_name": "dread"
  },
  "content": "pussy holds dick?",
  "timestamp": "2025-06-02T22:53:33.131000+00:00",
  "referenced_message": "1379230884426682468",
  "mentions": [
    {
      "id": 140880190158012417,
      "username": "cubox",
      "display_name": "Cubox"
    }
  ],
  "attachments": [],
  "reactions": []
},
{
  "id": "1379231550230630411",
  "author": {
    "id": 140880190158012417,
    "username": "cubox",
    "display_name": "Cubox"
  },
  "content": "no",
  "timestamp": "2025-06-02T22:53:46.458000+00:00",
  "referenced_message": null,
  "mentions": [],
  "attachments": [],
  "reactions": []
},
{
  "id": "1379232002603089930",
  "author": {
    "id": 727302962917015612,
    "username": "ytrk",
    "display_name": "Still Bavarian"
  },
  "content": "damn\ninternships are supposed to be an easy walk in the park",
  "timestamp": "2025-06-02T22:55:34.312000+00:00",
  "referenced_message": null,
  "mentions": [],
  "attachments": [],
  "reactions": []
},
{
  "id": "1379232127480102943",
  "author": {
    "id": 727302962917015612,
    "username": "ytrk",
    "display_name": "Still Bavarian"
  },
  "content": "you're given 8-12 weeks to do a 3 week project",
  "timestamp": "2025-06-02T22:56:04.085000+00:00",
  "referenced_message": null,
  "mentions": [],
  "attachments": [],
  "reactions": []
},
{
  "id": "1379232197403213955",
  "author": {
    "id": 489569435812757519,
    "username": "dread2much",
    "display_name": "dread"
  },
  "content": "yeah how am i gonna do the projcet without the multibody dynamics software",
  "timestamp": "2025-06-02T22:56:20.756000+00:00",
  "referenced_message": null,
  "mentions": [],
  "attachments": [],
  "reactions": []
},
{
  "id": "1379232230466912428",
  "author": {
    "id": 489569435812757519,
    "username": "dread2much",
    "display_name": "dread"
  },
  "content": "which is also an industry standard lol",
  "timestamp": "2025-06-02T22:56:28.639000+00:00",
  "referenced_message": null,
  "mentions": [],
  "attachments": [],
  "reactions": []
},
{
  "id": "1379232439599235234",
  "author": {
    "id": 727302962917015612,
    "username": "ytrk",
    "display_name": "Still Bavarian"
  },
  "content": "no ro for this guy",
  "timestamp": "2025-06-02T22:57:18.500000+00:00",
  "referenced_message": null,
  "mentions": [],
  "attachments": [],
  "reactions": []
},
{
  "id": "1379236341946257499",
  "author": {
    "id": 142496872152432651,
    "username": "erinisafox",
    "display_name": "Audrey"
  },
  "content": "You could also be gay",
  "timestamp": "2025-06-02T23:12:48.892000+00:00",
  "referenced_message": "1379232197403213955",
  "mentions": [],
  "attachments": [],
  "reactions": []
},
{
  "id": "1379236424976564334",
  "author": {
    "id": 142496872152432651,
    "username": "erinisafox",
    "display_name": "Audrey"
  },
  "content": "That worked for me during my time getting shafted by a black guy",
  "timestamp": "2025-06-02T23:13:08.688000+00:00",
  "referenced_message": null,
  "mentions": [],
  "attachments": [],
  "reactions": []
},
{
  "id": "1379236485122887720",
  "author": {
    "id": 489569435812757519,
    "username": "dread2much",
    "display_name": "dread"
  },
  "content": "It still works for you",
  "timestamp": "2025-06-02T23:13:23.028000+00:00",
  "referenced_message": null,
  "mentions": [],
  "attachments": [],
  "reactions": []
},
{
  "id": "1379236491854610543",
  "author": {
    "id": 142496872152432651,
    "username": "erinisafox",
    "display_name": "Audrey"
  },
  "content": "Just made my own simulation software <:KEKW:918001534376230922>",
  "timestamp": "2025-06-02T23:13:24.633000+00:00",
  "referenced_message": null,
  "mentions": [],
  "attachments": [],
  "reactions": []
},
{
  "id": "1379236535886680145",
  "author": {
    "id": 142496872152432651,
    "username": "erinisafox",
    "display_name": "Audrey"
  },
  "content": "No my current boss is a white woman",
  "timestamp": "2025-06-02T23:13:35.131000+00:00",
  "referenced_message": "1379236485122887720",
  "mentions": [],
  "attachments": [],
  "reactions": []
},
{
  "id": "1379236561035591806",
  "author": {
    "id": 489569435812757519,
    "username": "dread2much",
    "display_name": "dread"
  },
  "content": "Now you work at a base where gay army dudes just give each other handjobs while getting subsidized by the federal government",
  "timestamp": "2025-06-02T23:13:41.127000+00:00",
  "referenced_message": null,
  "mentions": [],
  "attachments": [],
  "reactions": [
    {
      "emoji": "<:pandanote:918001802333540362>",
      "users": [
        {
          "id": 142496872152432651,
          "username": "erinisafox",
          "display_name": "Audrey"
        }
      ]
    }
  ]
},
{
  "id": "1379243711547834488",
  "author": {
    "id": 1089928222252208170,
    "username": "autumnprincess",
    "display_name": "kanthru"
  },
  "content": "we spend 450 trillion dollars every year subsidizing hot gay army mens hand jobs and not enough people are talking about this",
  "timestamp": "2025-06-02T23:42:05.942000+00:00",
  "referenced_message": null,
  "mentions": [],
  "attachments": [],
  "reactions": []
},
{
  "id": "1379260320425447424",
  "author": {
    "id": 140880190158012417,
    "username": "cubox",
    "display_name": "Cubox"
  },
  "content": "that's not enough",
  "timestamp": "2025-06-03T00:48:05.807000+00:00",
  "referenced_message": null,
  "mentions": [],
  "attachments": [],
  "reactions": []
},
{
  "id": "1379437289687744585",
  "author": {
    "id": 529303103015485441,
    "username": "thamle",
    "display_name": "Math"
  },
  "content": "The keyword is hot",
  "timestamp": "2025-06-03T12:31:18.567000+00:00",
  "referenced_message": "1379243711547834488",
  "mentions": [
    {
      "id": 1089928222252208170,
      "username": "autumnprincess",
      "display_name": "kanthru"
    }
  ],
  "attachments": [],
  "reactions": []
},
{
  "id": "1379942744016818327",
  "author": {
    "id": 140880190158012417,
    "username": "cubox",
    "display_name": "Cubox"
  },
  "content": "two new doodle jumps are in the making",
  "timestamp": "2025-06-04T21:59:48.270000+00:00",
  "referenced_message": null,
  "mentions": [],
  "attachments": [],
  "reactions": [
    {
      "emoji": "<:pandanote:918001802333540362>",
      "users": [
        {
          "id": 142496872152432651,
          "username": "erinisafox",
          "display_name": "Audrey"
        }
      ]
    }
  ]
},
{
  "id": "1379948173027708958",
  "author": {
    "id": 140880190158012417,
    "username": "cubox",
    "display_name": "Cubox"
  },
  "content": "https://cubox.dev/files/jump/augment/",
  "timestamp": "2025-06-04T22:21:22.647000+00:00",
  "referenced_message": null,
  "mentions": [],
  "attachments": [],
  "reactions": []
},
{
  "id": "1379948193223147602",
  "author": {
    "id": 140880190158012417,
    "username": "cubox",
    "display_name": "Cubox"
  },
  "content": "https://cubox.dev/files/jump/sonnet4-refact/",
  "timestamp": "2025-06-04T22:21:27.462000+00:00",
  "referenced_message": null,
  "mentions": [],
  "attachments": [],
  "reactions": []
},
{
  "id": "1379951693235621969",
  "author": {
    "id": 727302962917015612,
    "username": "ytrk",
    "display_name": "Still Bavarian"
  },
  "content": "pretty good",
  "timestamp": "2025-06-04T22:35:21.930000+00:00",
  "referenced_message": null,
  "mentions": [],
  "attachments": [],
  "reactions": []
},
{
  "id": "1379952021523927141",
  "author": {
    "id": 727302962917015612,
    "username": "ytrk",
    "display_name": "Still Bavarian"
  },
  "content": "minor bug: with sonnet4: after you click play again, it doesn't track your keypresses properly",
  "timestamp": "2025-06-04T22:36:40.200000+00:00",
  "referenced_message": null,
  "mentions": [],
  "attachments": [],
  "reactions": []
},
{
  "id": "1379952052565704874",
  "author": {
    "id": 727302962917015612,
    "username": "ytrk",
    "display_name": "Still Bavarian"
  },
  "content": "it just keeps going 1 direction",
  "timestamp": "2025-06-04T22:36:47.601000+00:00",
  "referenced_message": null,
  "mentions": [],
  "attachments": [],
  "reactions": []
},
{
  "id": "1379952097776242900",
  "author": {
    "id": 727302962917015612,
    "username": "ytrk",
    "display_name": "Still Bavarian"
  },
  "content": "the AIs cannot replace us yet <:xita:1018997092611526706>",
  "timestamp": "2025-06-04T22:36:58.380000+00:00",
  "referenced_message": null,
  "mentions": [],
  "attachments": [],
  "reactions": []
},
{
  "id": "1379979100617310272",
  "author": {
    "id": 140880190158012417,
    "username": "cubox",
    "display_name": "Cubox"
  },
  "content": "with the refact one?",
  "timestamp": "2025-06-05T00:24:16.359000+00:00",
  "referenced_message": null,
  "mentions": [],
  "attachments": [],
  "reactions": []
},
{
  "id": "1379979516683751525",
  "author": {
    "id": 727302962917015612,
    "username": "ytrk",
    "display_name": "Still Bavarian"
  },
  "content": "yes",
  "timestamp": "2025-06-05T00:25:55.557000+00:00",
  "referenced_message": null,
  "mentions": [],
  "attachments": [],
  "reactions": []
},
{
  "id": "1380298501531107338",
  "author": {
    "id": 140880190158012417,
    "username": "cubox",
    "display_name": "Cubox"
  },
  "content": "new gemini is cooking",
  "timestamp": "2025-06-05T21:33:27.471000+00:00",
  "referenced_message": null,
  "mentions": [],
  "attachments": [
    "<attachment>"
  ],
  "reactions": [
    {
      "emoji": "<:pandanote:918001802333540362>",
      "users": [
        {
          "id": 248184987193442314,
          "username": "magma._.",
          "display_name": "magam"
        }
      ]
    }
  ]
},
{
  "id": "1380298690618720316",
  "author": {
    "id": 140880190158012417,
    "username": "cubox",
    "display_name": "Cubox"
  },
  "content": "no it didn't",
  "timestamp": "2025-06-05T21:34:12.553000+00:00",
  "referenced_message": null,
  "mentions": [],
  "attachments": [
    "<attachment>"
  ],
  "reactions": []
},
{
  "id": "1380298703931445278",
  "author": {
    "id": 140880190158012417,
    "username": "cubox",
    "display_name": "Cubox"
  },
  "content": "bro's inventing stuff",
  "timestamp": "2025-06-05T21:34:15.727000+00:00",
  "referenced_message": null,
  "mentions": [],
  "attachments": [],
  "reactions": []
},
{
  "id": "1380298725225922691",
  "author": {
    "id": 248184987193442314,
    "username": "magma._.",
    "display_name": "magam"
  },
  "content": "we're doomed as soon as ai learns to check stackoverflow",
  "timestamp": "2025-06-05T21:34:20.804000+00:00",
  "referenced_message": null,
  "mentions": [],
  "attachments": [],
  "reactions": [
    {
      "emoji": "<:xita:1018997092611526706>",
      "users": [
        {
          "id": 142496872152432651,
          "username": "erinisafox",
          "display_name": "Audrey"
        }
      ]
    }
  ]
},
{
  "id": "1380300181274103829",
  "author": {
    "id": 140880190158012417,
    "username": "cubox",
    "display_name": "Cubox"
  },
  "content": "it's broken",
  "timestamp": "2025-06-05T21:40:07.953000+00:00",
  "referenced_message": null,
  "mentions": [],
  "attachments": [],
  "reactions": [
    {
      "emoji": "😭",
      "users": [
        {
          "id": 142496872152432651,
          "username": "erinisafox",
          "display_name": "Audrey"
        }
      ]
    }
  ]
},
{
  "id": "1380306052351922176",
  "author": {
    "id": 727302962917015612,
    "username": "ytrk",
    "display_name": "Still Bavarian"
  },
  "content": "it already does",
  "timestamp": "2025-06-05T22:03:27.727000+00:00",
  "referenced_message": "1380298725225922691",
  "mentions": [],
  "attachments": [],
  "reactions": []
},
{
  "id": "1380306091023269918",
  "author": {
    "id": 727302962917015612,
    "username": "ytrk",
    "display_name": "Still Bavarian"
  },
  "content": "at least, agents should be able to search the internet for answers",
  "timestamp": "2025-06-05T22:03:36.947000+00:00",
  "referenced_message": null,
  "mentions": [],
  "attachments": [],
  "reactions": [
    {
      "emoji": "<:isee:918001433419333682>",
      "users": [
        {
          "id": 1216527387194298378,
          "username": "John",
          "display_name": "John"
        }
      ]
    }
  ]
},
{
  "id": "1380968494614577153",
  "author": {
    "id": 140880190158012417,
    "username": "cubox",
    "display_name": "Cubox"
  },
  "content": "john if you can read this react with a hi",
  "timestamp": "2025-06-07T17:55:46.270000+00:00",
  "referenced_message": null,
  "mentions": [],
  "attachments": [],
  "reactions": []
}
]

