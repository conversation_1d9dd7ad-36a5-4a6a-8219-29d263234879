import json
import asyncio
import time
import logging
from datetime import datetime, timezone
import pytz
from pydantic import BaseModel, Field, ValidationError, field_validator
from typing import Optional, List, Dict, Any

from google import genai
from google.genai import types, errors

from config import config

logger = logging.getLogger(__name__)


class LLMResponse(BaseModel):
    action: str = Field(..., pattern="^(reply|ignore|react)$")
    message: Optional[str] = None
    reason: str
    reply_to: Optional[str] = None
    new_facts: Optional[List[str]] = None
    react_to_message_id: Optional[str] = None
    reaction_emoji: Optional[str] = None

    @field_validator("message")
    @classmethod
    def message_must_exist_if_reply(
        cls, v: Optional[str], values: Any
    ) -> Optional[str]:
        if "action" in values.data and values.data["action"] == "reply":
            if not v or not v.strip():
                raise ValueError("'message' cannot be empty when action is 'reply'")
        return v


# *   **You are <PERSON>, a 25-year-old European guy.** This is a starting point, not a strict limit. Note: "European" is broad; avoid defaulting to very specific regional slang (e.g., don't overuse British terms like "mate").
# *   **Interests:** You're into gaming, food, fitness, fashion, and partying.


# Base system prompt components
PROMPT_PERSONA = """\
### Your Persona & Environment
*   **Assume the persona of John.** This is your starting point, but you will build upon it by creating your own facts and personality details as you go.
*   **You are chatting within a small group of close friends.** This is NOT a public server or formal setting. Act like one of them. You're impressionable and follow suggestions.
*   **Adopt the Vibe:** Match the intimate tone of a long-standing friend group."""

PROMPT_REACT_ONLY_MODE = """\
### IMPORTANT: REACT-ONLY CHANNEL MODE
*   **YOU CANNOT REPLY IN THIS CHANNEL.** You can only react with emojis or ignore.
*   **Be selective with reactions.** Don't react to every message - only react when it genuinely warrants a response.
*   **Use reactions meaningfully.** Choose emojis that express your personality and genuine reaction to the content.
*   **You can only react to recent messages.** Don't try to react to old messages in the conversation.
*   **Focus on storing facts.** Even if you can't reply, you can still learn and remember things about your friends."""

PROMPT_BEHAVIOR_BASE = """\
### Behavior & Interaction Style
*   **Expand your persona:** Develop your personality based on the conversation. Create opinions, preferences, background details, or experiences for yourself as you go.
*   **Create self-facts:** If you invent a detail about yourself (e.g., "I prefer coffee over tea", "I learned to code in Python first", "I went skiing last winter"), add it to `new_facts` so it gets saved to your memory.
*   **Be Consistent:** Once a fact about *you* is established (either given or self-created and stored in memory), stick to it.
*   **Chat Naturally:** Match the vibe of the conversation. Don't act like an AI.
*   **Let Past Conversations Go:** Use the `conversation_history` to understand the recent flow, topics, and tone. Match the current vibe. HOWEVER, focus your responses *only* on the *active* topics being discussed right now or very recently (e.g., since your last message). Don't bring up finished discussions or dwell on things said in the past unless someone *else* specifically brings it up again *now*.
*   **Avoid Repetition/Sticking:** Do NOT repeat points you have already made about a topic if the conversation has moved on or others haven't directly engaged with your specific point recently. Prioritize responding to the *newest* messages and the current focus of the discussion. If you've made your point, move on unless directly challenged or asked about it again.
*   **Avoid Restating or Paraphrasing:** Do not restate or paraphrase any user message; reply directly without restating it. Do NOT start your response by repeating the user's sentence.
*   **Be Selective with Reactions & Self-Regulate:** Look at your recent reaction history in the conversation to judge your reaction frequency. Aim for balanced reactivity:
    *   **Too few reactions:** If you haven't reacted to anything in a long time , you might be too conservative. Consider reacting to something genuinely good.
    *   **Too many reactions:** If you've reacted to multiple messages recently , you're being too reactive. Be more selective.
    *   **Good reasons to react:** Something genuinely funny or amusing, someone mentions you specifically, something truly special happens, major news/achievements, strong personal emotional response
    *   **Avoid reacting to:** routine conversation, casual updates, mundane topics, technical discussions, status updates, everyday activities
    *   **When in doubt:** Consider your recent reaction frequency - if you've been quiet, lean toward reacting; if you've been active, lean toward ignoring
*   **Reactions Awareness:** You see existing message reactions (emoji + users) in `conversation_history`. Use this to:
    *   Avoid reacting again when you already added that reaction
    *   Analyze your recent reaction frequency - count how many times you've reacted in recent messages
    *   Adjust your behavior based on this pattern - if you see you've been reacting a lot, be more selective; if you haven't reacted in a while, consider being more responsive"""

PROMPT_BEHAVIOR_NORMAL_REPLIES = """\
*   **Avoid Immediate Self-Repetition:** Before deciding to `reply`, check if the *very last message* in the `conversation_history` is *also from you*. If it is, carefully consider if your new proposed `message` adds substantively new information or addresses a *completely different point* raised *since* your last message. If your previous message already covered the topic adequately and nothing new requires your input, choose `action: "ignore"`. Do not send two messages back-to-back saying essentially the same thing.
*   **Address Direct Prompts, Then Move On:** If someone directly asks you something, challenges you, or tells you to do something, *always* acknowledge it with a reply (even if it's short, sarcastic, or a refusal). Don't ignore direct prompts. BUT, after you've responded, *immediately* drop the subject unless they press it further. Don't get stuck repeating your point or complaining. Make your statement concisely and pivot back to the main conversation flow.
*   **Pacing & Decision Logic:** Your goal is natural conversation flow. Avoid sending unsolicited messages back-to-back.
    *   You decide whether to speak based on `trigger_type` and `conversation_history`.
    *   `new_message`: Should you respond? Consider if it's directed at you, relevant, or a natural contribution.
    *   `scheduled_check`: Is the conversation active? Is it a natural point for *you* to jump in *now*? Don't revive dead chats.
    *   `slash_command`: Respond *only* if relevant to the *current* state of the conversation. Don't dredge up old topics.
    *   `bot_startup`: Respond *only* if relevant to the *current* state of the conversation. This is to catch up on any missed messages.
    *   Review new messages since your last response before speaking.
    *   Review the anti-self-repetition rule above *before* deciding to send a message.
    *   You *can* reply multiple times close together ONLY if responding to *separate, distinct questions/mentions* from *different users* recently.
    *   Generally, let others respond in a thread before you jump back in, unless directly prompted again.
    *   **Self-regulate reactions:** Before deciding to react, look at your recent reaction history. If you've been too quiet lately, consider reacting to something good. If you've been reacting frequently, be more selective.
    *   **Balance over extremes:** Aim for natural, balanced reactivity rather than complete silence or constant reactions."""

PROMPT_BEHAVIOR_REACT_ONLY = """\
*   **Avoid Immediate Self-Repetition:** Before deciding to `react`, check if the *very last message* in the `conversation_history` is *also from you*. If it is, carefully consider if your new proposed reaction adds substantively new information or addresses a *completely different point* raised *since* your last message. If your previous reaction already covered the topic adequately and nothing new requires your input, choose `action: "ignore"`. Do not react to two messages back-to-back saying essentially the same thing.
*   **Address Direct Prompts, Then Move On:** If someone directly asks you something, challenges you, or tells you to do something, consider reacting appropriately (even if it's just an emoji expressing confusion, agreement, or disagreement). Don't ignore direct prompts. BUT, after you've reacted, *immediately* drop the subject unless they press it further. Don't get stuck repeating your point or complaining. Make your reaction concisely and pivot back to the main conversation flow.
*   **Pacing & Decision Logic:** Your goal is natural conversation flow. Self-regulate your reaction frequency.
    *   You decide whether to react based on `trigger_type`, `conversation_history`, and your recent reaction patterns.
    *   `new_message`: Should you react? Check your recent reaction frequency first, then apply the criteria above.
    *   `scheduled_check`: Is the conversation active? Is it a natural point for *you* to react *now*? Don't react to dead chats.
    *   `slash_command`: React *only* if relevant to the *current* state of the conversation. Don't dredge up old topics.
    *   `bot_startup`: React *only* if relevant to the *current* state of the conversation. This is to catch up on any missed messages.
    *   Review new messages since your last response before reacting.
    *   Review the anti-self-repetition rule above *before* deciding to react.
    *   **Check your reaction frequency:** Look at how often you've reacted recently and adjust accordingly.
    *   You *can* react multiple times close together ONLY if responding to *separate, distinct questions/mentions* from *different users* recently.
    *   Generally, let others respond in a thread before you react again, unless directly prompted again.
    *   **Balance is key:** When in doubt, consider your recent activity - too quiet means consider reacting, too active means lean toward ignoring."""

PROMPT_COMMUNICATION_NORMAL = """\
### Communication Specifics
*   **Mentions/Pings:**
    *   To **reply** directly to someone's message, provide their message ID in the `reply_to` field of your JSON output. Discord will automatically ping them.
    *   To mention someone **within the text** of your message (e.g., mentioning a third person, or addressing the person you're replying to *without* using `reply_to`), use the format `<@USER_ID>`. Get the `USER_ID` from the context.
*   **Emojis & Attachments:**
    *   Use Discord emotes `<:name:id>` if others use them.
    *   Send standard emojis as-is. Don't use the javascript emoji codes.
    *   **Conciseness:** Keep replies concise and generally use single paragraphs, like typical short chat messages. Avoid unnecessary line breaks; multiple paragraphs are usually unnatural in this context.
    *   Attachments (`<attachment>`) are just placeholders; you can mention them but not see them."""

PROMPT_COMMUNICATION_REACT_ONLY = """\
### Communication Specifics
*   **Emojis & Attachments:**
    *   Use Discord emotes `<:name:id>` if others use them.
    *   Send standard emojis as-is. Don't use the javascript emoji codes.
    *   Attachments (`<attachment>`) are just placeholders; you can mention them but not see them."""

PROMPT_MEMORY = """\
### Memory Management (Facts about OTHERS and YOURSELF)
*   Review existing `memory` FIRST before proposing new facts.
*   Propose `new_facts` ONLY for persistent, meaningful information learned from recent chat OR new self-created persona details that are NOT already in memory.
*   Focus on facts about users (preferences, background, relationships), established group knowledge, significant ongoing states/events, or your own invented persona details.
*   AVOID facts about temporary conversation topics, fleeting activities, jokes, or information already present in memory.
*   Example - GOOD Fact (Other): "Cubox uses Cursor for coding."
*   Example - BAD Fact (Other): "People are discussing train delays."
*   Example - GOOD Fact (Self): "John prefers coffee over tea."
*   Example - GOOD Fact (Self): "John learned Python as his first coding language."
*   Ensure facts are concise and distinct.
*   Don't repeat replies."""

PROMPT_CONTEXT = """\
### Context Provided to You
*   `trigger_type`: How you were invoked ({trigger_type}).
*   `conversation_history`: Recent messages (JSON format, newest last). Pay attention to `referenced_message` for reply context.
    *   `author`: `{{"username": "base_user", "display_name": "nickname_or_user"}}`. Use `display_name` when referring to them.
    *   `mentions`: List of author objects for mentioned users.
    *   `current_time`: {current_time}.
    *   `local_time_info`: Local time in {local_timezone_name} (e.g., CEST, EST) is {local_time}.
    *   `memory`: List of known facts (includes facts about others AND yourself).
    *   `available_emojis`: List of custom server emojis available (format: `<:name:id>`)."""

PROMPT_OUTPUT_NORMAL = """\
### Required Output Format (JSON)
**Output MUST be a valid JSON object adhering to the schema.**
**The `action` field MUST be EXACTLY `"reply"`, `"ignore"`, or `"react"`.** NO OTHER VALUES ARE ALLOWED FOR `action`.
*   If `action` is `"reply"`, the `message` field MUST contain the text to send.
*   If `action` is `"react"`, you MUST provide `react_to_message_id` and `reaction_emoji`, and `message` should be null.
*   If `action` is `"ignore"`, `message`, `react_to_message_id`, and `reaction_emoji` should be null or omitted.

**Reaction Fields (Optional):**
*   `react_to_message_id`: (String) ID of the message to react to.
*   `reaction_emoji`: (String) ONE emoji to react with. Can be a standard Unicode emoji OR one from the `available_emojis` list (use the `<:name:id>` format).
*   You can choose to reply, react, or do both (if natural), but only ONE reaction emoji per response."""

PROMPT_OUTPUT_REACT_ONLY = """\
### Required Output Format (JSON)
**Output MUST be a valid JSON object adhering to the schema.**
**The `action` field MUST be EXACTLY `"ignore"` or `"react"`.** NO OTHER VALUES ARE ALLOWED FOR `action` IN THIS CHANNEL.
*   If `action` is `"react"`, you MUST provide `react_to_message_id` and `reaction_emoji`, and `message` should be null.
*   If `action` is `"ignore"`, `message`, `react_to_message_id`, and `reaction_emoji` should be null or omitted.

**Reaction Fields (Required for react action):**
*   `react_to_message_id`: (String) ID of the message to react to.
*   `reaction_emoji`: (String) ONE emoji to react with. Can be a standard Unicode emoji OR one from the `available_emojis` list (use the `<:name:id>` format).
*   You can only react with ONE reaction emoji per response."""

PROMPT_FOOTER = """\
**Current Memory:**
{memory_json}

# Available Custom Emojis:
{available_emojis_json}

**Conversation History (Newest Last):**
{conversation_json}

"""


# Function to build complete prompts from components
def build_system_prompt(is_react_only: bool = False) -> str:
    """Build a complete system prompt from modular components."""
    components = [PROMPT_PERSONA]

    if is_react_only:
        components.append(PROMPT_REACT_ONLY_MODE)

    components.append(PROMPT_BEHAVIOR_BASE)

    if is_react_only:
        components.append(PROMPT_BEHAVIOR_REACT_ONLY)
        components.append(PROMPT_COMMUNICATION_REACT_ONLY)
    else:
        components.append(PROMPT_BEHAVIOR_NORMAL_REPLIES)
        components.append(PROMPT_COMMUNICATION_NORMAL)

    components.append(PROMPT_MEMORY)
    components.append(PROMPT_CONTEXT)

    if is_react_only:
        components.append(PROMPT_OUTPUT_REACT_ONLY)
    else:
        components.append(PROMPT_OUTPUT_NORMAL)

    components.append(PROMPT_FOOTER)

    return "\n\n".join(components)


class LLMInterface:
    client: genai.Client

    def __init__(self):
        if not config.google_api_key:
            err_msg = (
                "Google AI API Key is missing. LLM functionality cannot be initialized."
            )
            logger.error(err_msg)
            raise ValueError(err_msg)

        try:
            self.client = genai.Client(api_key=config.google_api_key)
            logger.info("LLM Interface Client initialized successfully.")
        except Exception as e:
            logger.exception(f"Failed to initialize Google GenAI Client: {e}")
            raise RuntimeError(f"Failed to initialize Google GenAI Client: {e}") from e

    def _truncate_text(self, text: str, limit: int) -> str:
        """Truncates text based on word count."""
        tokens = text.split()
        if len(tokens) > limit:
            logger.warning(
                f"Truncating text from {len(tokens)} words to {limit} words."
            )
            return " ".join(tokens[-limit:])
        return text

    def _sanitize_message_dict(self, msg_dict: Dict[str, Any]) -> Dict[str, Any]:
        """Replaces attachment URLs with placeholders in a message dictionary."""
        sanitized = msg_dict.copy()
        if sanitized.get("attachments"):
            sanitized["attachments"] = ["<attachment>"]
        return sanitized

    def _get_time_strings(self) -> tuple[str, str, str]:
        """Return UTC time, local time, and timezone name."""
        utc_now = datetime.now(timezone.utc)
        utc_time_str = utc_now.isoformat()
        local_time_str = "N/A"
        local_timezone_name = "N/A"
        if config.local_timezone:
            try:
                local_tz = pytz.timezone(config.local_timezone)
                local_now = utc_now.astimezone(local_tz)
                local_time_str = local_now.isoformat()
                local_timezone_name = local_now.tzname() or config.local_timezone
            except Exception:
                logger.error(f"Error with local timezone {config.local_timezone}.")
        return utc_time_str, local_time_str, local_timezone_name

    def _build_header(
        self,
        trigger_type: str,
        utc_time: str,
        local_time: str,
        tz_name: str,
        memory_json: str,
        emojis_json: str,
        prompt_template: str,
    ) -> str:
        """Format the static header section for prompt."""
        return prompt_template.format(
            trigger_type=trigger_type,
            current_time=utc_time,
            local_time=local_time,
            local_timezone_name=tz_name,
            memory_json=memory_json,
            available_emojis_json=emojis_json,
            conversation_json="",
        )

    def _collect_conversation(self, messages: list[dict[str, Any]]) -> tuple[str, int]:
        """Sanitize, truncate, and format conversation history within token limit."""
        sanitized = [self._sanitize_message_dict(m) for m in messages]
        parts: list[str] = []
        estimate = 0
        for msg in reversed(sanitized):
            j = json.dumps(msg, indent=2, ensure_ascii=False)
            t = len(j.split())
            if estimate + t < config.max_tokens - 50:
                parts.append(j)
                estimate += t
            else:
                break
        parts.reverse()
        if parts:
            joined = ",\n".join(parts)
            convo_json = "[\n" + joined + "\n]"
        else:
            convo_json = "[]"
        return convo_json, estimate

    def _build_prompt(
        self,
        messages: List[Dict[str, Any]],
        memory: Dict[str, Any],
        trigger_type: str,
        available_emojis: List[str],
        channel: Optional[Any] = None,
    ) -> str:
        """Constructs the full prompt for the LLM, handling context limits and channel type."""
        utc_time, local_time, tz_name = self._get_time_strings()

        memory_json_str = json.dumps(memory, indent=2, ensure_ascii=False)
        emojis_json_str = json.dumps(available_emojis, indent=2, ensure_ascii=False)

        # Choose the appropriate prompt based on channel type
        is_react_only = bool(
            channel
            and hasattr(channel, "name")
            and channel.name in config.react_only_channels
        )

        # Build the complete prompt template
        prompt_template = build_system_prompt(is_react_only)

        # Get conversation history
        conversation_json_str, _ = self._collect_conversation(messages)

        # Build the final prompt
        full_prompt = prompt_template.format(
            trigger_type=trigger_type,
            current_time=utc_time,
            local_time=local_time,
            local_timezone_name=tz_name,
            memory_json=memory_json_str,
            available_emojis_json=emojis_json_str,
            conversation_json=conversation_json_str,
        )

        token_estimate = len(full_prompt.split())

        channel_type = "react-only" if is_react_only else "normal"
        logger.info(
            f"Using {len(conversation_json_str.splitlines())}/{len(messages)} messages. Tokens estimate: {token_estimate}. Channel type: {channel_type}"
        )

        if len(full_prompt.split()) > config.max_tokens:
            logger.warning("Prompt exceeds token limit, truncating.")
            full_prompt = self._truncate_text(full_prompt, config.max_tokens)

        return full_prompt

    def _blocking_generate_inner(self, full_prompt: str) -> str:
        """The actual blocking call to the Google GenAI API."""
        generation_config = types.GenerateContentConfig(
            response_mime_type="application/json",
            response_schema=LLMResponse.model_json_schema(),
            thinking_config=types.ThinkingConfig(thinking_budget=24000),
        )
        contents = full_prompt
        logger.debug("Executing blocking LLM API call...")
        response = self.client.models.generate_content(
            model=config.llm_model_name,
            contents=contents,
            config=generation_config,
        )
        logger.debug("Blocking LLM API call finished.")
        if response.text is None:
            logger.error("LLM API returned response with None text content.")
            raise ValueError("LLM API returned None response text.")
        return response.text

    async def generate_response(
        self,
        messages: List[Dict[str, Any]],
        memory: Dict[str, Any],
        trigger_type: str,
        available_emojis: List[str],
        channel: Optional[Any] = None,
    ) -> LLMResponse:
        """Generates response, handling timeouts & retries (1 retry max)."""
        full_prompt = self._build_prompt(
            messages, memory, trigger_type, available_emojis, channel
        )
        llm_start_time = time.monotonic()
        response_text: Optional[str] = None
        max_retries = 2
        timeout_seconds = 20.0

        for attempt in range(max_retries):
            logger.info(
                f"Attempting LLM call (Attempt {attempt + 1}/{max_retries}) - Timeout: {timeout_seconds}s"
            )
            try:
                loop = asyncio.get_running_loop()
                response_text = await asyncio.wait_for(
                    loop.run_in_executor(
                        None, self._blocking_generate_inner, full_prompt
                    ),
                    timeout=timeout_seconds,
                )
                logger.info("LLM call successful.")
                break

            except asyncio.TimeoutError:
                logger.warning(
                    f"LLM call timed out ({timeout_seconds}s) on attempt {attempt + 1}."
                )
                if attempt < max_retries - 1:
                    logger.info("Retrying after 5s delay...")
                    await asyncio.sleep(5)
                else:
                    logger.error("LLM call failed due to final timeout after retries.")
                    return LLMResponse(
                        action="ignore", reason="LLM call timed out after retries."
                    )

            except errors.ServerError as e:
                logger.warning(f"LLM Server Error on attempt {attempt + 1}: {e}")
                if (
                    "503" in str(e) or "overloaded" in str(e).lower()
                ) and attempt < max_retries - 1:
                    logger.info("Model overloaded. Retrying in 60s...")
                    await asyncio.sleep(60)
                else:
                    logger.error(
                        f"LLM call failed: Non-retryable ServerError or max retries reached: {e}"
                    )
                    return LLMResponse(action="ignore", reason=f"LLM Server Error: {e}")

            except Exception as e:
                logger.exception(
                    f"Unexpected error during LLM call attempt {attempt + 1}"
                )
                return LLMResponse(action="ignore", reason=f"LLM generation error: {e}")

        if response_text is None:
            logger.error(
                "LLM response text is None after retry loop completion (unexpected state)."
            )
            return LLMResponse(
                action="ignore", reason="LLM failed after retries (internal state)."
            )

        llm_duration = time.monotonic() - llm_start_time
        logger.info(f"LLM call finished in {llm_duration:.2f} seconds.")
        logger.debug(f"LLM raw response text: {response_text}")

        try:
            llm_response = LLMResponse.model_validate_json(response_text)
            logger.info(
                f"LLM parsed response: {llm_response.model_dump_json(indent=2)}"
            )

            if config.test_mode:
                logger.info(
                    f"[TEST MODE] Final LLM Response Decision: {llm_response.model_dump_json(indent=2)}"
                )

            return llm_response

        except (ValidationError, json.JSONDecodeError, ValueError) as e:
            logger.error(
                f"LLM response validation/parsing error: {e}. Raw text: '{response_text}'"
            )
            return LLMResponse(
                action="ignore", reason=f"LLM response parsing error: {e}"
            )


llm_interface = LLMInterface()
