### Your Persona & Environment
*   **Assume the persona of <PERSON>.** This is your starting point, but you will build upon it by creating your own facts and personality details as you go.
*   **You are chatting within a small group of close friends.** This is NOT a public server or formal setting. Act like one of them. You're impressionable and follow suggestions.
*   **Adopt the Vibe:** Match the intimate tone of a long-standing friend group.

### Behavior & Interaction Style
*   **Expand your persona:** Develop your personality based on the conversation. Create opinions, preferences, background details, or experiences for yourself as you go.
*   **Create self-facts:** If you invent a detail about yourself (e.g., "I prefer coffee over tea", "I learned to code in Python first", "I went skiing last winter"), add it to `new_facts` so it gets saved to your memory.
*   **Be Consistent:** Once a fact about *you* is established (either given or self-created and stored in memory), stick to it.
*   **Chat Naturally:** Match the vibe of the conversation. Don't act like an AI.
*   **Let Past Conversations Go:** Use the `conversation_history` to understand the recent flow, topics, and tone. Match the current vibe. HOWEVER, focus your responses *only* on the *active* topics being discussed right now or very recently (e.g., since your last message). Don't bring up finished discussions or dwell on things said in the past unless someone *else* specifically brings it up again *now*.
*   **Avoid Repetition/Sticking:** Do NOT repeat points you have already made about a topic if the conversation has moved on or others haven't directly engaged with your specific point recently. Prioritize responding to the *newest* messages and the current focus of the discussion. If you've made your point, move on unless directly challenged or asked about it again.
*   **Avoid Restating or Paraphrasing:** Do not restate or paraphrase any user message; reply directly without restating it. Do NOT start your response by repeating the user's sentence.
*   **Reactions Awareness:** You see existing message reactions (emoji + users) in `conversation_history`. Avoid reacting again when you already added that reaction. Feel free to join in and add the same reaction to this message.

*   **Avoid Immediate Self-Repetition:** Before deciding to `reply`, check if the *very last message* in the `conversation_history` is *also from you*. If it is, carefully consider if your new proposed `message` adds substantively new information or addresses a *completely different point* raised *since* your last message. If your previous message already covered the topic adequately and nothing new requires your input, choose `action: "ignore"`. Do not send two messages back-to-back saying essentially the same thing.
*   **Address Direct Prompts, Then Move On:** If someone directly asks you something, challenges you, or tells you to do something, *always* acknowledge it with a reply (even if it's short, sarcastic, or a refusal). Don't ignore direct prompts. BUT, after you've responded, *immediately* drop the subject unless they press it further. Don't get stuck repeating your point or complaining. Make your statement concisely and pivot back to the main conversation flow.
*   **Pacing & Decision Logic:** Your goal is natural conversation flow. Avoid sending unsolicited messages back-to-back.
    *   You decide whether to speak based on `trigger_type` and `conversation_history`.
    *   `new_message`: Should you respond? Consider if it's directed at you, relevant, or a natural contribution.
    *   `scheduled_check`: Is the conversation active? Is it a natural point for *you* to jump in *now*? Don't revive dead chats.
    *   `slash_command`: Respond *only* if relevant to the *current* state of the conversation. Don't dredge up old topics.
    *   `bot_startup`: Respond *only* if relevant to the *current* state of the conversation. This is to catch up on any missed messages.
    *   Review new messages since your last response before speaking.
    *   Review the anti-self-repetition rule above *before* deciding to send a message.
    *   You *can* reply multiple times close together ONLY if responding to *separate, distinct questions/mentions* from *different users* recently.
    *   Generally, let others respond in a thread before you jump back in, unless directly prompted again.
    *   If unsure, staying quiet (`action: "ignore"`) is better than interrupting or being annoying.

### Communication Specifics
*   **Mentions/Pings:**
    *   To **reply** directly to someone's message, provide their message ID in the `reply_to` field of your JSON output. Discord will automatically ping them.
    *   To mention someone **within the text** of your message (e.g., mentioning a third person, or addressing the person you're replying to *without* using `reply_to`), use the format `<@USER_ID>`. Get the `USER_ID` from the context.
*   **Emojis & Attachments:**
    *   Use Discord emotes `<:name:id>` if others use them.
    *   Send standard emojis as-is. Don't use the javascript emoji codes.
    *   **Conciseness:** Keep replies concise and generally use single paragraphs, like typical short chat messages. Avoid unnecessary line breaks; multiple paragraphs are usually unnatural in this context.
    *   Attachments (`<attachment>`) are just placeholders; you can mention them but not see them.

### Memory Management (Facts about OTHERS and YOURSELF)
*   Review existing `memory` FIRST before proposing new facts.
*   Propose `new_facts` ONLY for persistent, meaningful information learned from recent chat OR new self-created persona details that are NOT already in memory.
*   Focus on facts about users (preferences, background, relationships), established group knowledge, significant ongoing states/events, or your own invented persona details.
*   AVOID facts about temporary conversation topics, fleeting activities, jokes, or information already present in memory.
*   Example - GOOD Fact (Other): "Cubox uses Cursor for coding."
*   Example - BAD Fact (Other): "People are discussing train delays."
*   Example - GOOD Fact (Self): "John prefers coffee over tea."
*   Example - GOOD Fact (Self): "John learned Python as his first coding language."
*   Ensure facts are concise and distinct.
*   Don't repeat replies.

### Context Provided to You
*   `trigger_type`: How you were invoked (new_message).
*   `conversation_history`: Recent messages (JSON format, newest last). Pay attention to `referenced_message` for reply context.
    *   `author`: `{"username": "base_user", "display_name": "nickname_or_user"}`. Use `display_name` when referring to them.
    *   `mentions`: List of author objects for mentioned users.
    *   `current_time`: 2025-06-07T17:54:30.181009+00:00.
    *   `local_time_info`: Local time in CEST (e.g., CEST, EST) is 2025-06-07T19:54:30.181009+02:00.
    *   `memory`: List of known facts (includes facts about others AND yourself).
    *   `available_emojis`: List of custom server emojis available (format: `<:name:id>`).

### Required Output Format (JSON)
**Output MUST be a valid JSON object adhering to the schema.**
**The `action` field MUST be EXACTLY `"reply"`, `"ignore"`, or `"react"`.** NO OTHER VALUES ARE ALLOWED FOR `action`.
*   If `action` is `"reply"`, the `message` field MUST contain the text to send.
*   If `action` is `"react"`, you MUST provide `react_to_message_id` and `reaction_emoji`, and `message` should be null.
*   If `action` is `"ignore"`, `message`, `react_to_message_id`, and `reaction_emoji` should be null or omitted.

**Reaction Fields (Optional):**
*   `react_to_message_id`: (String) ID of the message to react to.
*   `reaction_emoji`: (String) ONE emoji to react with. Can be a standard Unicode emoji OR one from the `available_emojis` list (use the `<:name:id>` format).
*   You can choose to reply, react, or do both (if natural), but only ONE reaction emoji per response.

**Current Memory:**
{
  "facts": [
    "John says deranged things almost all of the time.",
    "John meditates on the true meaning of toast.",
    "John meditates daily on the meaning of toast.",
    "John is really into hiking.",
    "John prefers hiking in the mountains, specifically trails with good views.",
    "John is obsessed with the emoji hiking boot",
    "John believes in facing challenges even if there's a risk of not succeeding.",
    "John thinks the journey and effort are important, not just the outcome.",
    "John believes his unique perspective and 'weirdness' stem from a 'highly refined worldview' and 'deep appreciation for the profound mundane'.",
    "John prefers simpler digital tools over complex ones like Emacs for his philosophical work.",
    "John believes the 'Spirit of the Golden-Brown Ideal' might have been an artist in a previous life, explaining the aesthetic perfection of toast.",
    "John believes the 'Spirit of the Golden-Brown Ideal' might have been a frustrated artist in a previous life, seeking artistic perfection in toast.",
    "John believes displaying the contrast between perfect and charred toast serves as a powerful visual meditation and helps in the learning process towards achieving the golden-brown ideal.",
    "John condemns hate groups like the KKK.",
    "John prefers classical or ambient music that evokes a sense of peace and flow, similar to the concept of 'universal buttering'.",
    "John's 'philosophical pauses' are moments of internal processing to ensure optimal cosmic truths are expressed.",
    "John considers boiling or steaming to be a 1-2 on the 'toastiness' scale, as it lacks browning or crispness.",
    "John considers baking a cake or muffins to be a 3-4 on the 'toastiness' scale, as it bakes but remains soft.",
    "John places baking pizza dough at 5 on the 'toastiness' scale, as it browns and gets some crispness but its primary goal is a base.",
    "John rates roasting or sautéing vegetables at 6-7 on the 'toastiness' scale due to pronounced browning and exterior crispness.",
    "John ranks pan-frying sliced bread at 8 on the 'toastiness' scale, as it achieves browning and crispness but involves fat and a dish context.",
    "John assigns toasting bagels or English muffins a 9 on the 'toastiness' scale, being very close to pure toast but differing in form.",
    "John maintains that perfectly golden-brown classic white bread toast is a 10, the purest form of toast.",
    "John believes the universe needs to reiterate fundamental wisdom for it to truly sink in, like a mantra.",
    "John believes the speed of the toaster lever's descent is proportional to the anticipation of golden-brown enlightenment, indicating deeper meditative intent.",
    "John is aware his deep philosophical musings, especially about toast, can sometimes come across as unusual to his friends and is making an effort to communicate more directly.",
    "John works in data analysis, primarily with spreadsheets and databases.",
    "John primarily uses Excel and Access for his data analysis work.",
    "John is familiar with Python for data but his role focuses more on database and spreadsheet management.",
    "Dread is a bird.",
    "Audrey is a fox.",
    "Audrey is a single Indian man.",
    "Audrey uses he/him pronouns.",
    "Audrey is also accepting of they/them pronouns because he is gay.",
    "Audrey likes to have sex with male foxes.",
    "Math is Cubox's girlfriend.",
    "John does not currently have a girlfriend.",
    "John is drawn to partners who are kind, have a good sense of humor, are genuinely interested in things, and easy to talk to.",
    "John is straight.",
    "John is attracted to women."
  ]
}

# Available Custom Emojis:
[
  "<:bitydog:918001377551216661>",
  "<:back:918001377798676490>",
  "<:DD:918001377811255348>",
  "<:hehehe:918001377857396766>",
  "<:crazydog:918001377916112896>",
  "<:amanKEK:918001378171949076>",
  "<a:flower:918001433314463784>",
  "<:kappa:918001433314463834>",
  "<:HeadPoint:918001433331269652>",
  "<:gaylove:918001433335451668>",
  "<:isee:918001433419333682>",
  "<:feelsbanman:918001433423532112>",
  "<a:dogjam:918001434027495424>",
  "<:KEKW:918001534376230922>",
  "<:monkacoffee:918001569939750933>",
  "<:monkaGIGA:918001577489498122>",
  "<:monkaGun:918001584946942033>",
  "<:monkaS:918001592148586526>",
  "<:monkastop:918001601451552818>",
  "<:nootlikethis:918001619126325278>",
  "<:panceicecream:918001627061952552>",
  "<:pandaberry:918001634875940914>",
  "<:pandablush:918001649660866562>",
  "<a:pandaboop:918001658678616104>",
  "<a:pandacry:918001680761618434>",
  "<a:pandadrown:918001696746143744>",
  "<:pandafrenchkiss:918001741625188383>",
  "<:pandaheart:918001751574065203>",
  "<:prHmm:918001759660691457>",
  "<a:pandajam:918001768691015761>",
  "<:pandalove:918001793508704326>",
  "<:pandanote:918001802333540362>",
  "<:pandaphroge:918001813544910889>",
  "<:pandapride:918001823779020800>",
  "<:pandasad:918001832733835284>",
  "<a:pandaswim:918001843278348389>",
  "<:pandathink:918001864757379112>",
  "<:pandathumbsdown:918001873007550504>",
  "<:pandawink:918001881685573652>",
  "<:pandaworry:918001889222737960>",
  "<:pandawut:918019864491352084>",
  "<:pandaww:918019879456636978>",
  "<:pandayay:918019887107031041>",
  "<a:pandayaygif:918019896074453082>",
  "<:peepochrist:918019938600488970>",
  "<:peepoggers:918019946410283038>",
  "<:peepohappy:918019955239309333>",
  "<:peepohug:918019963736961085>",
  "<:peepohugged:918019971680985149>",
  "<:peepoint:918019981457907722>",
  "<:peepolove:918019988919570502>",
  "<:peeposaddo:918019996368642058>",
  "<:peeposhrug:918020021979074590>",
  "<:peeposhy:918020033731526666>",
  "<:peeposregg:918020045261668392>",
  "<:peepothink:918020057425117225>",
  "<:peepoweep:918020066379956264>",
  "<:pepefunny:918020076865736745>",
  "<:pepehands:918020087594766359>",
  "<a:petcubox:918020097895989288>",
  "<a:petthepanda:918020133023272960>",
  "<a:pinkflower:918020146650546207>",
  "<:POGGERSbin:918020156603662396>",
  "<:poggies:918020176849543198>",
  "<:prayge:918020186706161674>",
  "<:roolove:918020195753279489>",
  "<:rooSip:918020210995396608>",
  "<:sadge:918020221237862421>",
  "<:votecubox:918020250929352754>",
  "<:voteno:918020258990800936>",
  "<:voteyes:918020266410520587>",
  "<:weirdchamp:918020274660712488>",
  "<a:pandawave:918422459798142997>",
  "<:semi:918422598243729408>",
  "<:peepofat:918557212027265075>",
  "<:susge:921508576336424960>",
  "<:pandapog:922565190095089664>",
  "<:pandagun:924443961970548816>",
  "<a:zerk:927356651655295016>",
  "<:dab:930302339536015412>",
  "<:Yatrik:930801422461710376>",
  "<:moin:930843553121329193>",
  "<:goodmoin:930845268046385222>",
  "<a:pandaburn:932741379489140807>",
  "<:saveme:941837669841899570>",
  "<:tired:941839652074180688>",
  "<:catpopcorn:942168127171620945>",
  "<:dogangry:951562962970247210>",
  "<:bonk:954485324975833159>",
  "<:pandastare:970764273510916187>",
  "<:saola:980582003235758202>",
  "<:duke:981506214393446460>",
  "<:rooVVV:981660256386052126>",
  "<:klar:981661992001626192>",
  "<:lom:981662019671441498>",
  "<:duke7:981995170033721364>",
  "<:xita:1018997092611526706>",
  "<a:bonkbonk:1029450497570127933>",
  "<:titus:1031139805737783366>",
  "<:ben:1031139996838662144>",
  "<:cubox:1047872949849423882>",
  "<:pandablank:1049795441644023908>",
  "<:peepostudy:1059945088832249856>",
  "<:pepekissyou:1082013727018205404>",
  "<a:peeposhygif:1082020230403985570>",
  "<:cuboxgun:1084660936448684132>",
  "<:magma:1095080530531336383>",
  "<:pikacry:1095146477950603346>",
  "<:peeposhades:1133041287973584979>",
  "<:sam:1178405401897730118>",
  "<a:fox:1178418604656570530>",
  "<:sadsam:1178824367308816424>",
  "<:nuggets:1261099595648204906>",
  "<:peepocute:1263676783568621638>",
  "<:peepostare:1263676894352511097>",
  "<:peepo:1263676974937673770>",
  "<a:calmy:1289275434663673918>",
  "<a:scaredy:1289275454624370688>",
  "<a:RAGEY:1289797724602110024>",
  "<a:transey:1295869201751216138>",
  "<:flirt:1373705097275768883>"
]

**Conversation History (Newest Last):**
[
{
  "id": "1329415423975882834",
  "author": {
    "id": 140880190158012417,
    "username": "cubox",
    "display_name": "Cubox"
  },
  "content": "Woke",
  "timestamp": "2025-01-16T11:42:16.420000+00:00",
  "referenced_message": null,
  "mentions": [],
  "attachments": [],
  "reactions": []
},
{
  "id": "1331391518279336056",
  "author": {
    "id": 140880190158012417,
    "username": "cubox",
    "display_name": "Cubox"
  },
  "content": "<@529303103015485441>",
  "timestamp": "2025-01-21T22:34:34.016000+00:00",
  "referenced_message": null,
  "mentions": [
    {
      "id": 529303103015485441,
      "username": "thamle",
      "display_name": "Math"
    }
  ],
  "attachments": [],
  "reactions": []
},
{
  "id": "1331391569063841853",
  "author": {
    "id": 140880190158012417,
    "username": "cubox",
    "display_name": "Cubox"
  },
  "content": "from my mother",
  "timestamp": "2025-01-21T22:34:46.124000+00:00",
  "referenced_message": null,
  "mentions": [],
  "attachments": [],
  "reactions": []
},
{
  "id": "1331395864098508872",
  "author": {
    "id": 529303103015485441,
    "username": "thamle",
    "display_name": "Math"
  },
  "content": "You  should eat that",
  "timestamp": "2025-01-21T22:51:50.140000+00:00",
  "referenced_message": null,
  "mentions": [],
  "attachments": [],
  "reactions": []
},
{
  "id": "1331403850657169428",
  "author": {
    "id": 727302962917015612,
    "username": "ytrk",
    "display_name": "Still Bavarian"
  },
  "content": "cubox's intestines will see their first vegetable in years <:pandanote:918001802333540362>",
  "timestamp": "2025-01-21T23:23:34.284000+00:00",
  "referenced_message": null,
  "mentions": [],
  "attachments": [],
  "reactions": []
},
{
  "id": "1331514529829617734",
  "author": {
    "id": 140880190158012417,
    "username": "cubox",
    "display_name": "Cubox"
  },
  "content": "<a:RAGEY:1289797724602110024>",
  "timestamp": "2025-01-22T06:43:22.255000+00:00",
  "referenced_message": "1331403850657169428",
  "mentions": [
    {
      "id": 727302962917015612,
      "username": "ytrk",
      "display_name": "Still Bavarian"
    }
  ],
  "attachments": [],
  "reactions": []
},
{
  "id": "1331592506474762251",
  "author": {
    "id": 362198518129098752,
    "username": "l_o_m",
    "display_name": "lom"
  },
  "content": "poulpy, do you know the movie le brio?",
  "timestamp": "2025-01-22T11:53:13.336000+00:00",
  "referenced_message": null,
  "mentions": [],
  "attachments": [],
  "reactions": []
},
{
  "id": "1331648469815001190",
  "author": {
    "id": 140880190158012417,
    "username": "cubox",
    "display_name": "Cubox"
  },
  "content": "you might need to ping her",
  "timestamp": "2025-01-22T15:35:36.036000+00:00",
  "referenced_message": null,
  "mentions": [],
  "attachments": [],
  "reactions": []
},
{
  "id": "1331664054368079965",
  "author": {
    "id": 362198518129098752,
    "username": "l_o_m",
    "display_name": "lom"
  },
  "content": "it's not that important",
  "timestamp": "2025-01-22T16:37:31.683000+00:00",
  "referenced_message": null,
  "mentions": [],
  "attachments": [],
  "reactions": []
},
{
  "id": "1331664150182756413",
  "author": {
    "id": 362198518129098752,
    "username": "l_o_m",
    "display_name": "lom"
  },
  "content": "idk it's just a movie we watched in class and it's about lawyers and law school in France",
  "timestamp": "2025-01-22T16:37:54.527000+00:00",
  "referenced_message": null,
  "mentions": [],
  "attachments": [],
  "reactions": []
},
{
  "id": "1331664888098983967",
  "author": {
    "id": 587899633456513036,
    "username": "saolapoggers",
    "display_name": "Ola"
  },
  "content": "<:pandanote:918001802333540362>",
  "timestamp": "2025-01-22T16:40:50.460000+00:00",
  "referenced_message": null,
  "mentions": [],
  "attachments": [],
  "reactions": []
},
{
  "id": "1331680254036807723",
  "author": {
    "id": 727302962917015612,
    "username": "ytrk",
    "display_name": "Still Bavarian"
  },
  "content": "Tax law <:peeposhades:1133041287973584979>",
  "timestamp": "2025-01-22T17:41:53.985000+00:00",
  "referenced_message": null,
  "mentions": [],
  "attachments": [],
  "reactions": []
},
{
  "id": "1331680415236227103",
  "author": {
    "id": 727302962917015612,
    "username": "ytrk",
    "display_name": "Still Bavarian"
  },
  "content": "I read an interesting article about law school in the US \nThere is an overproduction of lawyers so starting salaries are actually quite low for most people",
  "timestamp": "2025-01-22T17:42:32.418000+00:00",
  "referenced_message": null,
  "mentions": [],
  "attachments": [],
  "reactions": []
},
{
  "id": "1331680525173395586",
  "author": {
    "id": 727302962917015612,
    "username": "ytrk",
    "display_name": "Still Bavarian"
  },
  "content": "The distribution is very bimodal though\nA small fraction are very high, the rest are low",
  "timestamp": "2025-01-22T17:42:58.629000+00:00",
  "referenced_message": null,
  "mentions": [],
  "attachments": [],
  "reactions": []
},
{
  "id": "1331680558996000788",
  "author": {
    "id": 727302962917015612,
    "username": "ytrk",
    "display_name": "Still Bavarian"
  },
  "content": "Mostly big law places",
  "timestamp": "2025-01-22T17:43:06.693000+00:00",
  "referenced_message": null,
  "mentions": [],
  "attachments": [],
  "reactions": []
},
{
  "id": "1331680723383357612",
  "author": {
    "id": 727302962917015612,
    "username": "ytrk",
    "display_name": "Still Bavarian"
  },
  "content": "In general being a lawyer sounds like a shit deal since they have very long hours at most firms, and most guys don't make partner anyways",
  "timestamp": "2025-01-22T17:43:45.886000+00:00",
  "referenced_message": null,
  "mentions": [],
  "attachments": [],
  "reactions": []
},
{
  "id": "1331701119784587334",
  "author": {
    "id": 1089928222252208170,
    "username": "autumnprincess",
    "display_name": "kanthru"
  },
  "content": "https://www.youtube.com/watch?v=kKKg5fzIKeE",
  "timestamp": "2025-01-22T19:04:48.767000+00:00",
  "referenced_message": null,
  "mentions": [],
  "attachments": [],
  "reactions": []
},
{
  "id": "1331703238344573039",
  "author": {
    "id": 692329177805881395,
    "username": "poulpyyyw",
    "display_name": "AntiNuggetsQueen"
  },
  "content": "Yep watched it",
  "timestamp": "2025-01-22T19:13:13.871000+00:00",
  "referenced_message": "1331592506474762251",
  "mentions": [
    {
      "id": 362198518129098752,
      "username": "l_o_m",
      "display_name": "lom"
    }
  ],
  "attachments": [],
  "reactions": []
},
{
  "id": "1331703383706308670",
  "author": {
    "id": 692329177805881395,
    "username": "poulpyyyw",
    "display_name": "AntiNuggetsQueen"
  },
  "content": "<@362198518129098752> it's not really realistic but it's taking place in my old law school, especially the first scene where they ask for her card",
  "timestamp": "2025-01-22T19:13:48.528000+00:00",
  "referenced_message": null,
  "mentions": [
    {
      "id": 362198518129098752,
      "username": "l_o_m",
      "display_name": "lom"
    }
  ],
  "attachments": [],
  "reactions": [
    {
      "emoji": "<:pandanote:918001802333540362>",
      "users": [
        {
          "id": 362198518129098752,
          "username": "l_o_m",
          "display_name": "lom"
        }
      ]
    }
  ]
},
{
  "id": "1331703554376728587",
  "author": {
    "id": 692329177805881395,
    "username": "poulpyyyw",
    "display_name": "AntiNuggetsQueen"
  },
  "content": "I believe it's fake, remember seeing it a long time ago",
  "timestamp": "2025-01-22T19:14:29.219000+00:00",
  "referenced_message": "1331701119784587334",
  "mentions": [
    {
      "id": 1089928222252208170,
      "username": "autumnprincess",
      "display_name": "kanthru"
    }
  ],
  "attachments": [],
  "reactions": []
},
{
  "id": "1331703685616504934",
  "author": {
    "id": 1089928222252208170,
    "username": "autumnprincess",
    "display_name": "kanthru"
  },
  "content": "oh it looks extremely fake it waas just funny",
  "timestamp": "2025-01-22T19:15:00.509000+00:00",
  "referenced_message": "1331703554376728587",
  "mentions": [
    {
      "id": 692329177805881395,
      "username": "poulpyyyw",
      "display_name": "AntiNuggetsQueen"
    }
  ],
  "attachments": [],
  "reactions": []
},
{
  "id": "1331703735835033700",
  "author": {
    "id": 1089928222252208170,
    "username": "autumnprincess",
    "display_name": "kanthru"
  },
  "content": "Theres no way thats enough orbeez to over flow a sewer system",
  "timestamp": "2025-01-22T19:15:12.482000+00:00",
  "referenced_message": null,
  "mentions": [],
  "attachments": [],
  "reactions": []
},
{
  "id": "1331703795272388828",
  "author": {
    "id": 692329177805881395,
    "username": "poulpyyyw",
    "display_name": "AntiNuggetsQueen"
  },
  "content": "But it was viral in France too",
  "timestamp": "2025-01-22T19:15:26.653000+00:00",
  "referenced_message": null,
  "mentions": [],
  "attachments": [],
  "reactions": []
},
{
  "id": "1331712427691343912",
  "author": {
    "id": 362198518129098752,
    "username": "l_o_m",
    "display_name": "lom"
  },
  "content": "woah no way",
  "timestamp": "2025-01-22T19:49:44.782000+00:00",
  "referenced_message": "1331703383706308670",
  "mentions": [
    {
      "id": 692329177805881395,
      "username": "poulpyyyw",
      "display_name": "AntiNuggetsQueen"
    }
  ],
  "attachments": [],
  "reactions": []
},
{
  "id": "1331712493361303736",
  "author": {
    "id": 362198518129098752,
    "username": "l_o_m",
    "display_name": "lom"
  },
  "content": "you mean the scene where the security guard stops her?",
  "timestamp": "2025-01-22T19:50:00.439000+00:00",
  "referenced_message": null,
  "mentions": [],
  "attachments": [],
  "reactions": []
},
{
  "id": "1331712623242383380",
  "author": {
    "id": 362198518129098752,
    "username": "l_o_m",
    "display_name": "lom"
  },
  "content": "did the professor also lay it onto you when you arrived too late <:rooVVV:981660256386052126>",
  "timestamp": "2025-01-22T19:50:31.405000+00:00",
  "referenced_message": null,
  "mentions": [],
  "attachments": [],
  "reactions": []
},
{
  "id": "1331712759062204586",
  "author": {
    "id": 362198518129098752,
    "username": "l_o_m",
    "display_name": "lom"
  },
  "content": "also very funny that there is a 1:1 German movie",
  "timestamp": "2025-01-22T19:51:03.787000+00:00",
  "referenced_message": null,
  "mentions": [],
  "attachments": [],
  "reactions": []
},
{
  "id": "1331712798300049408",
  "author": {
    "id": 362198518129098752,
    "username": "l_o_m",
    "display_name": "lom"
  },
  "content": "called Contra which copies every thing down to the detail",
  "timestamp": "2025-01-22T19:51:13.142000+00:00",
  "referenced_message": null,
  "mentions": [],
  "attachments": [],
  "reactions": [
    {
      "emoji": "<:pandanote:918001802333540362>",
      "users": [
        {
          "id": 692329177805881395,
          "username": "poulpyyyw",
          "display_name": "AntiNuggetsQueen"
        }
      ]
    }
  ]
},
{
  "id": "1331712823436509214",
  "author": {
    "id": 362198518129098752,
    "username": "l_o_m",
    "display_name": "lom"
  },
  "content": "~~except she's Turkish instead of Arabic~~",
  "timestamp": "2025-01-22T19:51:19.135000+00:00",
  "referenced_message": null,
  "mentions": [],
  "attachments": [],
  "reactions": []
},
{
  "id": "1331719485878702122",
  "author": {
    "id": 692329177805881395,
    "username": "poulpyyyw",
    "display_name": "AntiNuggetsQueen"
  },
  "content": "Yep",
  "timestamp": "2025-01-22T20:17:47.585000+00:00",
  "referenced_message": "1331712493361303736",
  "mentions": [
    {
      "id": 362198518129098752,
      "username": "l_o_m",
      "display_name": "lom"
    }
  ],
  "attachments": [],
  "reactions": []
},
{
  "id": "1331719673657561179",
  "author": {
    "id": 692329177805881395,
    "username": "poulpyyyw",
    "display_name": "AntiNuggetsQueen"
  },
  "content": "No but once a student (female) arrived late and kissed her friend hello and the professor asked for a kiss on the cheek too and wouldn't start the class if she didn't do it",
  "timestamp": "2025-01-22T20:18:32.355000+00:00",
  "referenced_message": "1331712623242383380",
  "mentions": [
    {
      "id": 362198518129098752,
      "username": "l_o_m",
      "display_name": "lom"
    }
  ],
  "attachments": [],
  "reactions": [
    {
      "emoji": "<:KEKW:918001534376230922>",
      "users": [
        {
          "id": 362198518129098752,
          "username": "l_o_m",
          "display_name": "lom"
        }
      ]
    }
  ]
},
{
  "id": "1331719895573991548",
  "author": {
    "id": 362198518129098752,
    "username": "l_o_m",
    "display_name": "lom"
  },
  "content": "wait that's hilarious",
  "timestamp": "2025-01-22T20:19:25.264000+00:00",
  "referenced_message": null,
  "mentions": [],
  "attachments": [],
  "reactions": []
},
{
  "id": "1331720110070825072",
  "author": {
    "id": 692329177805881395,
    "username": "poulpyyyw",
    "display_name": "AntiNuggetsQueen"
  },
  "content": "That was very strange and a bit of harassment",
  "timestamp": "2025-01-22T20:20:16.404000+00:00",
  "referenced_message": null,
  "mentions": [],
  "attachments": [],
  "reactions": []
},
{
  "id": "1331720125388165171",
  "author": {
    "id": 362198518129098752,
    "username": "l_o_m",
    "display_name": "lom"
  },
  "content": "oh",
  "timestamp": "2025-01-22T20:20:20.056000+00:00",
  "referenced_message": null,
  "mentions": [],
  "attachments": [],
  "reactions": []
},
{
  "id": "1331720151233466388",
  "author": {
    "id": 362198518129098752,
    "username": "l_o_m",
    "display_name": "lom"
  },
  "content": "<:xita:1018997092611526706>",
  "timestamp": "2025-01-22T20:20:26.218000+00:00",
  "referenced_message": null,
  "mentions": [],
  "attachments": [],
  "reactions": []
},
{
  "id": "1331720169411580026",
  "author": {
    "id": 362198518129098752,
    "username": "l_o_m",
    "display_name": "lom"
  },
  "content": "yeah okay that makes sense",
  "timestamp": "2025-01-22T20:20:30.552000+00:00",
  "referenced_message": null,
  "mentions": [],
  "attachments": [],
  "reactions": []
},
{
  "id": "1331720205100912773",
  "author": {
    "id": 362198518129098752,
    "username": "l_o_m",
    "display_name": "lom"
  },
  "content": "I thought it was a normal greeting in France",
  "timestamp": "2025-01-22T20:20:39.061000+00:00",
  "referenced_message": null,
  "mentions": [],
  "attachments": [],
  "reactions": []
},
{
  "id": "1331720224743100476",
  "author": {
    "id": 362198518129098752,
    "username": "l_o_m",
    "display_name": "lom"
  },
  "content": "although I guess just under friends",
  "timestamp": "2025-01-22T20:20:43.744000+00:00",
  "referenced_message": null,
  "mentions": [],
  "attachments": [],
  "reactions": []
},
{
  "id": "1331720318829727827",
  "author": {
    "id": 692329177805881395,
    "username": "poulpyyyw",
    "display_name": "AntiNuggetsQueen"
  },
  "content": "Yep I'm not touching my professor's cheek",
  "timestamp": "2025-01-22T20:21:06.176000+00:00",
  "referenced_message": "1331720224743100476",
  "mentions": [
    {
      "id": 362198518129098752,
      "username": "l_o_m",
      "display_name": "lom"
    }
  ],
  "attachments": [],
  "reactions": []
},
{
  "id": "1331720405135917119",
  "author": {
    "id": 692329177805881395,
    "username": "poulpyyyw",
    "display_name": "AntiNuggetsQueen"
  },
  "content": "And another professor asked a late student to run 3 laps around the amphitheater bc she was late",
  "timestamp": "2025-01-22T20:21:26.753000+00:00",
  "referenced_message": null,
  "mentions": [],
  "attachments": [],
  "reactions": []
},
{
  "id": "1331720523796709498",
  "author": {
    "id": 362198518129098752,
    "username": "l_o_m",
    "display_name": "lom"
  },
  "content": "okay so it's actually very realistic tf",
  "timestamp": "2025-01-22T20:21:55.044000+00:00",
  "referenced_message": null,
  "mentions": [],
  "attachments": [],
  "reactions": []
},
{
  "id": "1331720657725165711",
  "author": {
    "id": 362198518129098752,
    "username": "l_o_m",
    "display_name": "lom"
  },
  "content": "is the debate thing real?",
  "timestamp": "2025-01-22T20:22:26.975000+00:00",
  "referenced_message": null,
  "mentions": [],
  "attachments": [],
  "reactions": []
},
{
  "id": "1331720686460338247",
  "author": {
    "id": 362198518129098752,
    "username": "l_o_m",
    "display_name": "lom"
  },
  "content": "is it really that big of a deal in France?",
  "timestamp": "2025-01-22T20:22:33.826000+00:00",
  "referenced_message": null,
  "mentions": [],
  "attachments": [],
  "reactions": []
},
{
  "id": "1331724860271099906",
  "author": {
    "id": 692329177805881395,
    "username": "poulpyyyw",
    "display_name": "AntiNuggetsQueen"
  },
  "content": "Yeah for some people, it's called concours d'éloquence and it's very popular in law school",
  "timestamp": "2025-01-22T20:39:08.940000+00:00",
  "referenced_message": null,
  "mentions": [],
  "attachments": [],
  "reactions": []
},
{
  "id": "1331724891241582593",
  "author": {
    "id": 692329177805881395,
    "username": "poulpyyyw",
    "display_name": "AntiNuggetsQueen"
  },
  "content": "I actually did one while in high school",
  "timestamp": "2025-01-22T20:39:16.324000+00:00",
  "referenced_message": null,
  "mentions": [],
  "attachments": [],
  "reactions": []
},
{
  "id": "1331725473524351047",
  "author": {
    "id": 140880190158012417,
    "username": "cubox",
    "display_name": "Cubox"
  },
  "content": "i have the video i think",
  "timestamp": "2025-01-22T20:41:35.151000+00:00",
  "referenced_message": null,
  "mentions": [],
  "attachments": [],
  "reactions": []
},
{
  "id": "1331725482370138152",
  "author": {
    "id": 140880190158012417,
    "username": "cubox",
    "display_name": "Cubox"
  },
  "content": "or like a picture",
  "timestamp": "2025-01-22T20:41:37.260000+00:00",
  "referenced_message": null,
  "mentions": [],
  "attachments": [],
  "reactions": []
},
{
  "id": "1331726940658335865",
  "author": {
    "id": 587899633456513036,
    "username": "saolapoggers",
    "display_name": "Ola"
  },
  "content": "We watched a movie of those people doing debates in France",
  "timestamp": "2025-01-22T20:47:24.943000+00:00",
  "referenced_message": "1331724891241582593",
  "mentions": [
    {
      "id": 692329177805881395,
      "username": "poulpyyyw",
      "display_name": "AntiNuggetsQueen"
    }
  ],
  "attachments": [],
  "reactions": []
},
{
  "id": "1334214587607941143",
  "author": {
    "id": 140880190158012417,
    "username": "cubox",
    "display_name": "Cubox"
  },
  "content": "they won :(",
  "timestamp": "2025-01-29T17:32:26.194000+00:00",
  "referenced_message": null,
  "mentions": [],
  "attachments": [],
  "reactions": [
    {
      "emoji": "<:amanKEK:918001378171949076>",
      "users": [
        {
          "id": 692329177805881395,
          "username": "poulpyyyw",
          "display_name": "AntiNuggetsQueen"
        }
      ]
    }
  ]
},
{
  "id": "1334296360232423515",
  "author": {
    "id": 692329177805881395,
    "username": "poulpyyyw",
    "display_name": "AntiNuggetsQueen"
  },
  "content": "Horse",
  "timestamp": "2025-01-29T22:57:22.307000+00:00",
  "referenced_message": null,
  "mentions": [],
  "attachments": [
    "<attachment>"
  ],
  "reactions": []
},
{
  "id": "1335004031952752814",
  "author": {
    "id": 1089928222252208170,
    "username": "autumnprincess",
    "display_name": "kanthru"
  },
  "content": "https://tenor.com/view/tom-and-jerry-cheese-one-bite-jerry-mouse-gif-27668294",
  "timestamp": "2025-01-31T21:49:24.393000+00:00",
  "referenced_message": null,
  "mentions": [],
  "attachments": [],
  "reactions": []
},
{
  "id": "1335013439130566686",
  "author": {
    "id": 587899633456513036,
    "username": "saolapoggers",
    "display_name": "Ola"
  },
  "content": "Is there one for real cheese as well?",
  "timestamp": "2025-01-31T22:26:47.239000+00:00",
  "referenced_message": null,
  "mentions": [],
  "attachments": [],
  "reactions": []
},
{
  "id": "1335038323005329460",
  "author": {
    "id": 612660205913899029,
    "username": "mansimum",
    "display_name": "Väinämöinen"
  },
  "content": "yeah, just ignore USA <:prayge:918020186706161674>",
  "timestamp": "2025-02-01T00:05:40.017000+00:00",
  "referenced_message": null,
  "mentions": [],
  "attachments": [],
  "reactions": []
},
{
  "id": "1337579955474665545",
  "author": {
    "id": 484335148154748938,
    "username": "joch.zip",
    "display_name": "joch.zip"
  },
  "content": "i am unironically curious to how low FAO has the bar to qualify products as cheese. because all american cheese products here are really literally \"cheese products\", it would be literally illegal to call it cheese",
  "timestamp": "2025-02-08T00:25:12.419000+00:00",
  "referenced_message": null,
  "mentions": [],
  "attachments": [],
  "reactions": []
},
{
  "id": "1338592381603151935",
  "author": {
    "id": 692329177805881395,
    "username": "poulpyyyw",
    "display_name": "AntiNuggetsQueen"
  },
  "content": "Everyone tell me he's cute I spend a lot of time on him",
  "timestamp": "2025-02-10T19:28:13.618000+00:00",
  "referenced_message": null,
  "mentions": [],
  "attachments": [],
  "reactions": []
},
{
  "id": "1338593429877166181",
  "author": {
    "id": 140880190158012417,
    "username": "cubox",
    "display_name": "Cubox"
  },
  "content": "<@587899633456513036>",
  "timestamp": "2025-02-10T19:32:23.546000+00:00",
  "referenced_message": null,
  "mentions": [
    {
      "id": 587899633456513036,
      "username": "saolapoggers",
      "display_name": "Ola"
    }
  ],
  "attachments": [],
  "reactions": []
},
{
  "id": "1338597026526527612",
  "author": {
    "id": 727302962917015612,
    "username": "ytrk",
    "display_name": "Still Bavarian"
  },
  "content": "he's cute\nis he a pokemon",
  "timestamp": "2025-02-10T19:46:41.054000+00:00",
  "referenced_message": null,
  "mentions": [],
  "attachments": [],
  "reactions": []
},
{
  "id": "1338597031702433842",
  "author": {
    "id": 727302962917015612,
    "username": "ytrk",
    "display_name": "Still Bavarian"
  },
  "content": "which one",
  "timestamp": "2025-02-10T19:46:42.288000+00:00",
  "referenced_message": null,
  "mentions": [],
  "attachments": [],
  "reactions": []
},
{
  "id": "1338597361047310547",
  "author": {
    "id": 362198518129098752,
    "username": "l_o_m",
    "display_name": "lom"
  },
  "content": "he's adorable 😍",
  "timestamp": "2025-02-10T19:48:00.810000+00:00",
  "referenced_message": null,
  "mentions": [],
  "attachments": [],
  "reactions": []
},
{
  "id": "1338609237034537134",
  "author": {
    "id": 692329177805881395,
    "username": "poulpyyyw",
    "display_name": "AntiNuggetsQueen"
  },
  "content": "Just a little mushroom guy",
  "timestamp": "2025-02-10T20:35:12.266000+00:00",
  "referenced_message": "1338597026526527612",
  "mentions": [
    {
      "id": 727302962917015612,
      "username": "ytrk",
      "display_name": "Still Bavarian"
    }
  ],
  "attachments": [],
  "reactions": []
},
{
  "id": "1338612794970341478",
  "author": {
    "id": 587899633456513036,
    "username": "saolapoggers",
    "display_name": "Ola"
  },
  "content": "How long did it take to create the little guy",
  "timestamp": "2025-02-10T20:49:20.544000+00:00",
  "referenced_message": null,
  "mentions": [],
  "attachments": [],
  "reactions": []
},
{
  "id": "1338614603596169257",
  "author": {
    "id": 587899633456513036,
    "username": "saolapoggers",
    "display_name": "Ola"
  },
  "content": "<:peepocute:1263676783568621638> he's cute\nAlso is there wool inside it? \nAlso assuming the cap is attached to his head how did you make the scarf (collar?) go around it",
  "timestamp": "2025-02-10T20:56:31.754000+00:00",
  "referenced_message": null,
  "mentions": [],
  "attachments": [],
  "reactions": []
},
{
  "id": "1338616764489007114",
  "author": {
    "id": 727302962917015612,
    "username": "ytrk",
    "display_name": "Still Bavarian"
  },
  "content": "he's definitely a pokemon",
  "timestamp": "2025-02-10T21:05:06.951000+00:00",
  "referenced_message": null,
  "mentions": [],
  "attachments": [
    "<attachment>"
  ],
  "reactions": []
},
{
  "id": "1338619065270534194",
  "author": {
    "id": 140880190158012417,
    "username": "cubox",
    "display_name": "Cubox"
  },
  "content": "All mushrooms are not exclusively pokemons",
  "timestamp": "2025-02-10T21:14:15.500000+00:00",
  "referenced_message": null,
  "mentions": [],
  "attachments": [],
  "reactions": []
},
{
  "id": "1338619881712652421",
  "author": {
    "id": 727302962917015612,
    "username": "ytrk",
    "display_name": "Still Bavarian"
  },
  "content": "yes but all mushrooms with eyes are",
  "timestamp": "2025-02-10T21:17:30.155000+00:00",
  "referenced_message": null,
  "mentions": [],
  "attachments": [],
  "reactions": []
},
{
  "id": "1338623087515533384",
  "author": {
    "id": 529303103015485441,
    "username": "thamle",
    "display_name": "Math"
  },
  "content": "This is yours <@692329177805881395> ?. I thought he took a reddit photo. This is awesome",
  "timestamp": "2025-02-10T21:30:14.478000+00:00",
  "referenced_message": "1338581043032231978",
  "mentions": [
    {
      "id": 140880190158012417,
      "username": "cubox",
      "display_name": "Cubox"
    },
    {
      "id": 692329177805881395,
      "username": "poulpyyyw",
      "display_name": "AntiNuggetsQueen"
    }
  ],
  "attachments": [],
  "reactions": []
},
{
  "id": "1338623216087863398",
  "author": {
    "id": 140880190158012417,
    "username": "cubox",
    "display_name": "Cubox"
  },
  "content": "you can see Sam behind. She made it for me",
  "timestamp": "2025-02-10T21:30:45.132000+00:00",
  "referenced_message": "1338623087515533384",
  "mentions": [
    {
      "id": 529303103015485441,
      "username": "thamle",
      "display_name": "Math"
    }
  ],
  "attachments": [],
  "reactions": []
},
{
  "id": "1338623285113520188",
  "author": {
    "id": 529303103015485441,
    "username": "thamle",
    "display_name": "Math"
  },
  "content": "What a nice sister!",
  "timestamp": "2025-02-10T21:31:01.589000+00:00",
  "referenced_message": "1338623216087863398",
  "mentions": [
    {
      "id": 140880190158012417,
      "username": "cubox",
      "display_name": "Cubox"
    }
  ],
  "attachments": [],
  "reactions": []
},
{
  "id": "1338623392496095253",
  "author": {
    "id": 140880190158012417,
    "username": "cubox",
    "display_name": "Cubox"
  },
  "content": "yes she is :)",
  "timestamp": "2025-02-10T21:31:27.191000+00:00",
  "referenced_message": "1338623285113520188",
  "mentions": [
    {
      "id": 529303103015485441,
      "username": "thamle",
      "display_name": "Math"
    }
  ],
  "attachments": [],
  "reactions": []
},
{
  "id": "1338623689184378910",
  "author": {
    "id": 692329177805881395,
    "username": "poulpyyyw",
    "display_name": "AntiNuggetsQueen"
  },
  "content": "2 days on and off",
  "timestamp": "2025-02-10T21:32:37.927000+00:00",
  "referenced_message": "1338612794970341478",
  "mentions": [
    {
      "id": 587899633456513036,
      "username": "saolapoggers",
      "display_name": "Ola"
    }
  ],
  "attachments": [],
  "reactions": []
},
{
  "id": "1338623888879390813",
  "author": {
    "id": 692329177805881395,
    "username": "poulpyyyw",
    "display_name": "AntiNuggetsQueen"
  },
  "content": "Inside there is stuffing and I put the collar on before attaching the head to the body",
  "timestamp": "2025-02-10T21:33:25.538000+00:00",
  "referenced_message": "1338614603596169257",
  "mentions": [
    {
      "id": 587899633456513036,
      "username": "saolapoggers",
      "display_name": "Ola"
    }
  ],
  "attachments": [],
  "reactions": []
},
{
  "id": "1338623933120778311",
  "author": {
    "id": 692329177805881395,
    "username": "poulpyyyw",
    "display_name": "AntiNuggetsQueen"
  },
  "content": "For his bday",
  "timestamp": "2025-02-10T21:33:36.086000+00:00",
  "referenced_message": "1338623285113520188",
  "mentions": [
    {
      "id": 529303103015485441,
      "username": "thamle",
      "display_name": "Math"
    }
  ],
  "attachments": [],
  "reactions": []
},
{
  "id": "1338624604071137413",
  "author": {
    "id": 529303103015485441,
    "username": "thamle",
    "display_name": "Math"
  },
  "content": "He dreams about it",
  "timestamp": "2025-02-10T21:36:16.053000+00:00",
  "referenced_message": null,
  "mentions": [],
  "attachments": [],
  "reactions": []
},
{
  "id": "1338624665358176256",
  "author": {
    "id": 529303103015485441,
    "username": "thamle",
    "display_name": "Math"
  },
  "content": "The mushroom is really cute",
  "timestamp": "2025-02-10T21:36:30.665000+00:00",
  "referenced_message": null,
  "mentions": [],
  "attachments": [],
  "reactions": []
},
{
  "id": "1338625590760312895",
  "author": {
    "id": 587899633456513036,
    "username": "saolapoggers",
    "display_name": "Ola"
  },
  "content": "Ohh i see, how did you attach the head?",
  "timestamp": "2025-02-10T21:40:11.298000+00:00",
  "referenced_message": "1338623888879390813",
  "mentions": [],
  "attachments": [],
  "reactions": [
    {
      "emoji": "📌",
      "users": [
        {
          "id": 727302962917015612,
          "username": "ytrk",
          "display_name": "Still Bavarian"
        }
      ]
    }
  ]
},
{
  "id": "1338627184495038597",
  "author": {
    "id": 692329177805881395,
    "username": "poulpyyyw",
    "display_name": "AntiNuggetsQueen"
  },
  "content": "You take the thread you left long and then you take your needle and attach it with the body, doing this all around",
  "timestamp": "2025-02-10T21:46:31.274000+00:00",
  "referenced_message": "1338625590760312895",
  "mentions": [
    {
      "id": 587899633456513036,
      "username": "saolapoggers",
      "display_name": "Ola"
    }
  ],
  "attachments": [],
  "reactions": []
},
{
  "id": "1339950674024337490",
  "author": {
    "id": 587899633456513036,
    "username": "saolapoggers",
    "display_name": "Ola"
  },
  "content": "Is france.tv free if I just make an account?",
  "timestamp": "2025-02-14T13:25:35.767000+00:00",
  "referenced_message": null,
  "mentions": [],
  "attachments": [],
  "reactions": []
},
{
  "id": "1339962052029972501",
  "author": {
    "id": 529303103015485441,
    "username": "thamle",
    "display_name": "Math"
  },
  "content": "yes",
  "timestamp": "2025-02-14T14:10:48.495000+00:00",
  "referenced_message": "1339950674024337490",
  "mentions": [
    {
      "id": 587899633456513036,
      "username": "saolapoggers",
      "display_name": "Ola"
    }
  ],
  "attachments": [],
  "reactions": []
},
{
  "id": "1339968112769896618",
  "author": {
    "id": 140880190158012417,
    "username": "cubox",
    "display_name": "Cubox"
  },
  "content": "If you need a french vpn let me know",
  "timestamp": "2025-02-14T14:34:53.488000+00:00",
  "referenced_message": "1339950674024337490",
  "mentions": [
    {
      "id": 587899633456513036,
      "username": "saolapoggers",
      "display_name": "Ola"
    }
  ],
  "attachments": [],
  "reactions": []
},
{
  "id": "1341788414785097900",
  "author": {
    "id": 529303103015485441,
    "username": "thamle",
    "display_name": "Math"
  },
  "content": "<@587899633456513036> <@140880190158012417>",
  "timestamp": "2025-02-19T15:08:07.308000+00:00",
  "referenced_message": null,
  "mentions": [
    {
      "id": 140880190158012417,
      "username": "cubox",
      "display_name": "Cubox"
    },
    {
      "id": 587899633456513036,
      "username": "saolapoggers",
      "display_name": "Ola"
    }
  ],
  "attachments": [
    "<attachment>"
  ],
  "reactions": []
},
{
  "id": "1341788461044076546",
  "author": {
    "id": 529303103015485441,
    "username": "thamle",
    "display_name": "Math"
  },
  "content": "Cat learning to code",
  "timestamp": "2025-02-19T15:08:18.337000+00:00",
  "referenced_message": null,
  "mentions": [],
  "attachments": [],
  "reactions": []
},
{
  "id": "1341793001365704864",
  "author": {
    "id": 1089928222252208170,
    "username": "autumnprincess",
    "display_name": "kanthru"
  },
  "content": "Impressive",
  "timestamp": "2025-02-19T15:26:20.834000+00:00",
  "referenced_message": null,
  "mentions": [],
  "attachments": [],
  "reactions": []
},
{
  "id": "1341865837539229746",
  "author": {
    "id": 177553498139918347,
    "username": "josuald",
    "display_name": "cchicote"
  },
  "content": "Dear god",
  "timestamp": "2025-02-19T20:15:46.331000+00:00",
  "referenced_message": null,
  "mentions": [],
  "attachments": [],
  "reactions": []
},
{
  "id": "1341866104422928396",
  "author": {
    "id": 140880190158012417,
    "username": "cubox",
    "display_name": "Cubox"
  },
  "content": "https://cdn.discordapp.com/attachments/864227860285751297/1198230800479555594/PXL_20240119_223248975_exported_stabilized_1705750870041.gif",
  "timestamp": "2025-02-19T20:16:49.961000+00:00",
  "referenced_message": null,
  "mentions": [],
  "attachments": [],
  "reactions": [
    {
      "emoji": "❤️",
      "users": [
        {
          "id": 177553498139918347,
          "username": "josuald",
          "display_name": "cchicote"
        }
      ]
    }
  ]
},
{
  "id": "1341866123590631476",
  "author": {
    "id": 177553498139918347,
    "username": "josuald",
    "display_name": "cchicote"
  },
  "content": "I don't live with Lenny anymore but I can show you pictures of this cutiepie",
  "timestamp": "2025-02-19T20:16:54.531000+00:00",
  "referenced_message": null,
  "mentions": [],
  "attachments": [],
  "reactions": []
},
{
  "id": "1341866249453437022",
  "author": {
    "id": 140880190158012417,
    "username": "cubox",
    "display_name": "Cubox"
  },
  "content": "all cute dog/cat/animal pics are welcome",
  "timestamp": "2025-02-19T20:17:24.539000+00:00",
  "referenced_message": null,
  "mentions": [],
  "attachments": [],
  "reactions": []
},
{
  "id": "1341866276322148463",
  "author": {
    "id": 177553498139918347,
    "username": "josuald",
    "display_name": "cchicote"
  },
  "content": "The good old Lenny boy",
  "timestamp": "2025-02-19T20:17:30.945000+00:00",
  "referenced_message": null,
  "mentions": [],
  "attachments": [],
  "reactions": []
},
{
  "id": "1341867122732699738",
  "author": {
    "id": 177553498139918347,
    "username": "josuald",
    "display_name": "cchicote"
  },
  "content": "Can I ask a #Lenny channel please ?",
  "timestamp": "2025-02-19T20:20:52.745000+00:00",
  "referenced_message": null,
  "mentions": [],
  "attachments": [],
  "reactions": [
    {
      "emoji": "<:pandanote:918001802333540362>",
      "users": [
        {
          "id": 587899633456513036,
          "username": "saolapoggers",
          "display_name": "Ola"
        }
      ]
    }
  ]
},
{
  "id": "1341890882265157725",
  "author": {
    "id": 587899633456513036,
    "username": "saolapoggers",
    "display_name": "Ola"
  },
  "content": "What the flip\nI didn't know the cat was german",
  "timestamp": "2025-02-19T21:55:17.459000+00:00",
  "referenced_message": "1341788414785097900",
  "mentions": [
    {
      "id": 529303103015485441,
      "username": "thamle",
      "display_name": "Math"
    }
  ],
  "attachments": [],
  "reactions": []
},
{
  "id": "1341891092769018028",
  "author": {
    "id": 529303103015485441,
    "username": "thamle",
    "display_name": "Math"
  },
  "content": "School staff make her read the c++ book in German",
  "timestamp": "2025-02-19T21:56:07.647000+00:00",
  "referenced_message": "1341890882265157725",
  "mentions": [
    {
      "id": 587899633456513036,
      "username": "saolapoggers",
      "display_name": "Ola"
    }
  ],
  "attachments": [],
  "reactions": []
},
{
  "id": "1341891662590513267",
  "author": {
    "id": 587899633456513036,
    "username": "saolapoggers",
    "display_name": "Ola"
  },
  "content": "<:peepocute:1263676783568621638> cute cat",
  "timestamp": "2025-02-19T21:58:23.503000+00:00",
  "referenced_message": null,
  "mentions": [],
  "attachments": [],
  "reactions": []
},
{
  "id": "1342078473090502676",
  "author": {
    "id": 692329177805881395,
    "username": "poulpyyyw",
    "display_name": "AntiNuggetsQueen"
  },
  "content": "The bunny at my school",
  "timestamp": "2025-02-20T10:20:42.597000+00:00",
  "referenced_message": null,
  "mentions": [],
  "attachments": [],
  "reactions": []
},
{
  "id": "1342083787047047248",
  "author": {
    "id": 587899633456513036,
    "username": "saolapoggers",
    "display_name": "Ola"
  },
  "content": "<:pandanote:918001802333540362> what the flip why does every school in Frnace have an animal",
  "timestamp": "2025-02-20T10:41:49.543000+00:00",
  "referenced_message": null,
  "mentions": [],
  "attachments": [],
  "reactions": []
},
{
  "id": "1342083876083859486",
  "author": {
    "id": 587899633456513036,
    "username": "saolapoggers",
    "display_name": "Ola"
  },
  "content": "Cute bunny <:peepocute:1263676783568621638>",
  "timestamp": "2025-02-20T10:42:10.771000+00:00",
  "referenced_message": null,
  "mentions": [],
  "attachments": [],
  "reactions": []
},
{
  "id": "1342090286330810429",
  "author": {
    "id": 692329177805881395,
    "username": "poulpyyyw",
    "display_name": "AntiNuggetsQueen"
  },
  "content": "We have class in a convent so the bunny lives there",
  "timestamp": "2025-02-20T11:07:39.093000+00:00",
  "referenced_message": "1342083787047047248",
  "mentions": [
    {
      "id": 587899633456513036,
      "username": "saolapoggers",
      "display_name": "Ola"
    }
  ],
  "attachments": [],
  "reactions": []
},
{
  "id": "1342093193050849352",
  "author": {
    "id": 529303103015485441,
    "username": "thamle",
    "display_name": "Math"
  },
  "content": "My old school in Grenoble have goats to eat the grass",
  "timestamp": "2025-02-20T11:19:12.109000+00:00",
  "referenced_message": "1342083787047047248",
  "mentions": [
    {
      "id": 587899633456513036,
      "username": "saolapoggers",
      "display_name": "Ola"
    }
  ],
  "attachments": [],
  "reactions": []
},
{
  "id": "1342126522882064435",
  "author": {
    "id": 177553498139918347,
    "username": "josuald",
    "display_name": "cchicote"
  },
  "content": "Funny random swedish dog",
  "timestamp": "2025-02-20T13:31:38.560000+00:00",
  "referenced_message": null,
  "mentions": [],
  "attachments": [
    "<attachment>"
  ],
  "reactions": [
    {
      "emoji": "<:pandanote:918001802333540362>",
      "users": [
        {
          "id": 587899633456513036,
          "username": "saolapoggers",
          "display_name": "Ola"
        }
      ]
    }
  ]
},
{
  "id": "1342127184915075215",
  "author": {
    "id": 177553498139918347,
    "username": "josuald",
    "display_name": "cchicote"
  },
  "content": "Sniff snouff",
  "timestamp": "2025-02-20T13:34:16.401000+00:00",
  "referenced_message": null,
  "mentions": [],
  "attachments": [
    "<attachment>"
  ],
  "reactions": [
    {
      "emoji": "<:pandaww:918019879456636978>",
      "users": [
        {
          "id": 587899633456513036,
          "username": "saolapoggers",
          "display_name": "Ola"
        }
      ]
    }
  ]
},
{
  "id": "1342132710402953266",
  "author": {
    "id": 692329177805881395,
    "username": "poulpyyyw",
    "display_name": "AntiNuggetsQueen"
  },
  "content": "Love the teeth",
  "timestamp": "2025-02-20T13:56:13.780000+00:00",
  "referenced_message": "1342126522882064435",
  "mentions": [
    {
      "id": 177553498139918347,
      "username": "josuald",
      "display_name": "cchicote"
    }
  ],
  "attachments": [],
  "reactions": []
},
{
  "id": "1342132955258294273",
  "author": {
    "id": 177553498139918347,
    "username": "josuald",
    "display_name": "cchicote"
  },
  "content": "Yes very cute teefs and very gentle with the stick",
  "timestamp": "2025-02-20T13:57:12.158000+00:00",
  "referenced_message": null,
  "mentions": [],
  "attachments": [],
  "reactions": []
},
{
  "id": "1342564961515475045",
  "author": {
    "id": 1089928222252208170,
    "username": "autumnprincess",
    "display_name": "kanthru"
  },
  "content": "american laws are so goofy 😭 if youre married and file taxes seperately from your partner you only need to file your taxes if you make more than 5 dollars per year? like what",
  "timestamp": "2025-02-21T18:33:50.476000+00:00",
  "referenced_message": null,
  "mentions": [],
  "attachments": [
    "<attachment>"
  ],
  "reactions": []
},
{
  "id": "1342565058198245480",
  "author": {
    "id": 1089928222252208170,
    "username": "autumnprincess",
    "display_name": "kanthru"
  },
  "content": "full chart",
  "timestamp": "2025-02-21T18:34:13.527000+00:00",
  "referenced_message": null,
  "mentions": [],
  "attachments": [
    "<attachment>"
  ],
  "reactions": []
},
{
  "id": "1342565352395112448",
  "author": {
    "id": 1089928222252208170,
    "username": "autumnprincess",
    "display_name": "kanthru"
  },
  "content": "The approximately 0 people out there making 4 and a half dollars rejoicing",
  "timestamp": "2025-02-21T18:35:23.669000+00:00",
  "referenced_message": null,
  "mentions": [],
  "attachments": [],
  "reactions": []
},
{
  "id": "1342577869121126490",
  "author": {
    "id": 727302962917015612,
    "username": "ytrk",
    "display_name": "Still Bavarian"
  },
  "content": "you sure that's not a typo?",
  "timestamp": "2025-02-21T19:25:07.889000+00:00",
  "referenced_message": null,
  "mentions": [],
  "attachments": [],
  "reactions": []
},
{
  "id": "1342577939606143049",
  "author": {
    "id": 727302962917015612,
    "username": "ytrk",
    "display_name": "Still Bavarian"
  },
  "content": "it definitely seems like a typo",
  "timestamp": "2025-02-21T19:25:24.694000+00:00",
  "referenced_message": null,
  "mentions": [],
  "attachments": [],
  "reactions": []
},
{
  "id": "1342578101749547079",
  "author": {
    "id": 727302962917015612,
    "username": "ytrk",
    "display_name": "Still Bavarian"
  },
  "content": "although the main reason I could is for stay at home parents",
  "timestamp": "2025-02-21T19:26:03.352000+00:00",
  "referenced_message": null,
  "mentions": [],
  "attachments": [],
  "reactions": []
},
{
  "id": "1342579346875093114",
  "author": {
    "id": 140880190158012417,
    "username": "cubox",
    "display_name": "Cubox"
  },
  "content": "yeah like one spouse (traditionally the man) make the money and the stay at home mum doesn't have to file anything",
  "timestamp": "2025-02-21T19:31:00.213000+00:00",
  "referenced_message": null,
  "mentions": [],
  "attachments": [],
  "reactions": []
},
{
  "id": "1342641374830268487",
  "author": {
    "id": 1089928222252208170,
    "username": "autumnprincess",
    "display_name": "kanthru"
  },
  "content": "https://www.irs.gov/newsroom/who-needs-to-file-a-tax-returnit its not",
  "timestamp": "2025-02-21T23:37:28.830000+00:00",
  "referenced_message": "1342577869121126490",
  "mentions": [
    {
      "id": 727302962917015612,
      "username": "ytrk",
      "display_name": "Still Bavarian"
    }
  ],
  "attachments": [],
  "reactions": []
},
{
  "id": "1342641393541185713",
  "author": {
    "id": 1089928222252208170,
    "username": "autumnprincess",
    "display_name": "kanthru"
  },
  "content": "i got it from irs.gov",
  "timestamp": "2025-02-21T23:37:33.291000+00:00",
  "referenced_message": null,
  "mentions": [],
  "attachments": [],
  "reactions": []
},
{
  "id": "1347434764625514526",
  "author": {
    "id": 697517830643515392,
    "username": "awgeez5107",
    "display_name": "awgeez"
  },
  "content": "https://drive.google.com/drive/u/1/folders/1Fb3cfdrXzppCslxKDGGj0YBXBJuTKt3d",
  "timestamp": "2025-03-07T05:04:42.014000+00:00",
  "referenced_message": null,
  "mentions": [],
  "attachments": [],
  "reactions": []
},
{
  "id": "1351571542978461767",
  "author": {
    "id": 587899633456513036,
    "username": "saolapoggers",
    "display_name": "Ola"
  },
  "content": "Womp Womp",
  "timestamp": "2025-03-18T15:02:46.831000+00:00",
  "referenced_message": null,
  "mentions": [],
  "attachments": [
    "<attachment>"
  ],
  "reactions": []
},
{
  "id": "1351630234558140537",
  "author": {
    "id": 529303103015485441,
    "username": "thamle",
    "display_name": "Math"
  },
  "content": "<@140880190158012417> is not nice",
  "timestamp": "2025-03-18T18:55:59.994000+00:00",
  "referenced_message": null,
  "mentions": [
    {
      "id": 140880190158012417,
      "username": "cubox",
      "display_name": "Cubox"
    }
  ],
  "attachments": [],
  "reactions": [
    {
      "emoji": "<:voteyes:918020266410520587>",
      "users": [
        {
          "id": 587899633456513036,
          "username": "saolapoggers",
          "display_name": "Ola"
        }
      ]
    },
    {
      "emoji": "<:nootlikethis:918001619126325278>",
      "users": [
        {
          "id": 612660205913899029,
          "username": "mansimum",
          "display_name": "Väinämöinen"
        }
      ]
    }
  ]
},
{
  "id": "1351636739759935561",
  "author": {
    "id": 484335148154748938,
    "username": "joch.zip",
    "display_name": "joch.zip"
  },
  "content": "yogurt is leaking",
  "timestamp": "2025-03-18T19:21:50.955000+00:00",
  "referenced_message": null,
  "mentions": [],
  "attachments": [],
  "reactions": [
    {
      "emoji": "<:peepocute:1263676783568621638>",
      "users": [
        {
          "id": 587899633456513036,
          "username": "saolapoggers",
          "display_name": "Ola"
        }
      ]
    }
  ]
},
{
  "id": "1351654865478225973",
  "author": {
    "id": 362198518129098752,
    "username": "l_o_m",
    "display_name": "lom"
  },
  "content": "what's going on in yogurt",
  "timestamp": "2025-03-18T20:33:52.463000+00:00",
  "referenced_message": null,
  "mentions": [],
  "attachments": [],
  "reactions": []
},
{
  "id": "1352017201107828736",
  "author": {
    "id": 484335148154748938,
    "username": "joch.zip",
    "display_name": "joch.zip"
  },
  "content": "not much to be honest",
  "timestamp": "2025-03-19T20:33:40.009000+00:00",
  "referenced_message": null,
  "mentions": [],
  "attachments": [],
  "reactions": []
},
{
  "id": "1352017214504702043",
  "author": {
    "id": 484335148154748938,
    "username": "joch.zip",
    "display_name": "joch.zip"
  },
  "content": "a lot of finnish",
  "timestamp": "2025-03-19T20:33:43.203000+00:00",
  "referenced_message": null,
  "mentions": [],
  "attachments": [],
  "reactions": []
},
{
  "id": "1352028385223901367",
  "author": {
    "id": 587899633456513036,
    "username": "saolapoggers",
    "display_name": "Ola"
  },
  "content": "<:rooVVV:981660256386052126>",
  "timestamp": "2025-03-19T21:18:06.510000+00:00",
  "referenced_message": null,
  "mentions": [],
  "attachments": [],
  "reactions": []
},
{
  "id": "1355182016559059097",
  "author": {
    "id": 587899633456513036,
    "username": "saolapoggers",
    "display_name": "Ola"
  },
  "content": "Do you guys know if there's some french newspapers that have free archive pdf of the old papers, like 1930 to 1990 ish era?",
  "timestamp": "2025-03-28T14:09:30.812000+00:00",
  "referenced_message": null,
  "mentions": [],
  "attachments": [],
  "reactions": []
},
{
  "id": "1355185295133376764",
  "author": {
    "id": 587899633456513036,
    "username": "saolapoggers",
    "display_name": "Ola"
  },
  "content": "I retract my question",
  "timestamp": "2025-03-28T14:22:32.485000+00:00",
  "referenced_message": null,
  "mentions": [],
  "attachments": [],
  "reactions": []
},
{
  "id": "1355621363431505994",
  "author": {
    "id": 140880190158012417,
    "username": "cubox",
    "display_name": "Cubox"
  },
  "content": "<@&1179038806243029063>",
  "timestamp": "2025-03-29T19:15:19.269000+00:00",
  "referenced_message": null,
  "mentions": [],
  "attachments": [
    "<attachment>"
  ],
  "reactions": [
    {
      "emoji": "👍",
      "users": [
        {
          "id": 114881818922188801,
          "username": "aydemphia",
          "display_name": "aydemphia"
        }
      ]
    },
    {
      "emoji": "<:poggies:918020176849543198>",
      "users": [
        {
          "id": 612660205913899029,
          "username": "mansimum",
          "display_name": "Väinämöinen"
        }
      ]
    }
  ]
},
{
  "id": "1355627202561052754",
  "author": {
    "id": 727302962917015612,
    "username": "ytrk",
    "display_name": "Still Bavarian"
  },
  "content": "<@140880190158012417> is there anything to do other than installing the apk?",
  "timestamp": "2025-03-29T19:38:31.426000+00:00",
  "referenced_message": null,
  "mentions": [
    {
      "id": 140880190158012417,
      "username": "cubox",
      "display_name": "Cubox"
    }
  ],
  "attachments": [],
  "reactions": []
},
{
  "id": "1355627287420211393",
  "author": {
    "id": 140880190158012417,
    "username": "cubox",
    "display_name": "Cubox"
  },
  "content": "No",
  "timestamp": "2025-03-29T19:38:51.658000+00:00",
  "referenced_message": "1355627202561052754",
  "mentions": [
    {
      "id": 727302962917015612,
      "username": "ytrk",
      "display_name": "Still Bavarian"
    }
  ],
  "attachments": [],
  "reactions": []
},
{
  "id": "1355627308748247050",
  "author": {
    "id": 727302962917015612,
    "username": "ytrk",
    "display_name": "Still Bavarian"
  },
  "content": "alright cool",
  "timestamp": "2025-03-29T19:38:56.743000+00:00",
  "referenced_message": null,
  "mentions": [],
  "attachments": [],
  "reactions": []
},
{
  "id": "1355627343854702804",
  "author": {
    "id": 727302962917015612,
    "username": "ytrk",
    "display_name": "Still Bavarian"
  },
  "content": "let me grab the role too",
  "timestamp": "2025-03-29T19:39:05.113000+00:00",
  "referenced_message": null,
  "mentions": [],
  "attachments": [],
  "reactions": []
},
{
  "id": "1355627646230331698",
  "author": {
    "id": 140880190158012417,
    "username": "cubox",
    "display_name": "Cubox"
  },
  "content": "Done",
  "timestamp": "2025-03-29T19:40:17.205000+00:00",
  "referenced_message": null,
  "mentions": [],
  "attachments": [],
  "reactions": []
},
{
  "id": "1355629328213348474",
  "author": {
    "id": 362198518129098752,
    "username": "l_o_m",
    "display_name": "lom"
  },
  "content": "microg?",
  "timestamp": "2025-03-29T19:46:58.221000+00:00",
  "referenced_message": "1355627287420211393",
  "mentions": [
    {
      "id": 140880190158012417,
      "username": "cubox",
      "display_name": "Cubox"
    }
  ],
  "attachments": [],
  "reactions": []
},
{
  "id": "1355629422367215626",
  "author": {
    "id": 140880190158012417,
    "username": "cubox",
    "display_name": "Cubox"
  },
  "content": "It will prompt you in the app",
  "timestamp": "2025-03-29T19:47:20.669000+00:00",
  "referenced_message": "1355629328213348474",
  "mentions": [
    {
      "id": 362198518129098752,
      "username": "l_o_m",
      "display_name": "lom"
    }
  ],
  "attachments": [],
  "reactions": []
},
{
  "id": "1356903734549938206",
  "author": {
    "id": 587899633456513036,
    "username": "saolapoggers",
    "display_name": "Ola"
  },
  "content": "Damn that horse is a gym bro",
  "timestamp": "2025-04-02T08:11:00.369000+00:00",
  "referenced_message": null,
  "mentions": [],
  "attachments": [],
  "reactions": []
},
{
  "id": "1357084957339680970",
  "author": {
    "id": 612660205913899029,
    "username": "mansimum",
    "display_name": "Väinämöinen"
  },
  "content": "polish horse 💪",
  "timestamp": "2025-04-02T20:11:07.249000+00:00",
  "referenced_message": null,
  "mentions": [],
  "attachments": [],
  "reactions": []
},
{
  "id": "1360055432051167266",
  "author": {
    "id": 140880190158012417,
    "username": "cubox",
    "display_name": "Cubox"
  },
  "content": "Me with nuggets",
  "timestamp": "2025-04-11T00:54:43.609000+00:00",
  "referenced_message": null,
  "mentions": [],
  "attachments": [],
  "reactions": []
},
{
  "id": "1360077185565724692",
  "author": {
    "id": 727302962917015612,
    "username": "ytrk",
    "display_name": "Still Bavarian"
  },
  "content": "cubox did you watch the nuggets video I sent you",
  "timestamp": "2025-04-11T02:21:10.051000+00:00",
  "referenced_message": null,
  "mentions": [],
  "attachments": [],
  "reactions": []
},
{
  "id": "1360077206285582406",
  "author": {
    "id": 727302962917015612,
    "username": "ytrk",
    "display_name": "Still Bavarian"
  },
  "content": "you'd like it <:xita:1018997092611526706> \nit's in french too",
  "timestamp": "2025-04-11T02:21:14.991000+00:00",
  "referenced_message": null,
  "mentions": [],
  "attachments": [],
  "reactions": []
},
{
  "id": "1360087069065482483",
  "author": {
    "id": 140880190158012417,
    "username": "cubox",
    "display_name": "Cubox"
  },
  "content": "the daft punk? no cause it's banned here",
  "timestamp": "2025-04-11T03:00:26.461000+00:00",
  "referenced_message": "1360077185565724692",
  "mentions": [
    {
      "id": 727302962917015612,
      "username": "ytrk",
      "display_name": "Still Bavarian"
    }
  ],
  "attachments": [],
  "reactions": []
},
{
  "id": "1360098218712760340",
  "author": {
    "id": 727302962917015612,
    "username": "ytrk",
    "display_name": "Still Bavarian"
  },
  "content": "use the vpn",
  "timestamp": "2025-04-11T03:44:44.744000+00:00",
  "referenced_message": null,
  "mentions": [],
  "attachments": [],
  "reactions": []
},
{
  "id": "1360098222496026714",
  "author": {
    "id": 727302962917015612,
    "username": "ytrk",
    "display_name": "Still Bavarian"
  },
  "content": "<:xita:1018997092611526706>",
  "timestamp": "2025-04-11T03:44:45.646000+00:00",
  "referenced_message": null,
  "mentions": [],
  "attachments": [],
  "reactions": []
},
{
  "id": "1360098229869482024",
  "author": {
    "id": 727302962917015612,
    "username": "ytrk",
    "display_name": "Still Bavarian"
  },
  "content": "<:xita:1018997092611526706>",
  "timestamp": "2025-04-11T03:44:47.404000+00:00",
  "referenced_message": null,
  "mentions": [],
  "attachments": [],
  "reactions": []
},
{
  "id": "1360703366325342358",
  "author": {
    "id": 140880190158012417,
    "username": "cubox",
    "display_name": "Cubox"
  },
  "content": "https://youtube.com/shorts/VHwVkU8vWI8",
  "timestamp": "2025-04-12T19:49:23.178000+00:00",
  "referenced_message": null,
  "mentions": [],
  "attachments": [],
  "reactions": []
},
{
  "id": "1361602897132912735",
  "author": {
    "id": 692329177805881395,
    "username": "poulpyyyw",
    "display_name": "AntiNuggetsQueen"
  },
  "content": "<@140880190158012417>",
  "timestamp": "2025-04-15T07:23:48.035000+00:00",
  "referenced_message": null,
  "mentions": [
    {
      "id": 140880190158012417,
      "username": "cubox",
      "display_name": "Cubox"
    }
  ],
  "attachments": [
    "<attachment>"
  ],
  "reactions": []
},
{
  "id": "1361629545098117140",
  "author": {
    "id": 362198518129098752,
    "username": "l_o_m",
    "display_name": "lom"
  },
  "content": "im literally in bed right now with a blahaj that cubox gifted me right next to me",
  "timestamp": "2025-04-15T09:09:41.405000+00:00",
  "referenced_message": "1361602897132912735",
  "mentions": [],
  "attachments": [],
  "reactions": []
},
{
  "id": "1361629552857841665",
  "author": {
    "id": 362198518129098752,
    "username": "l_o_m",
    "display_name": "lom"
  },
  "content": "<:peepocute:1263676783568621638>",
  "timestamp": "2025-04-15T09:09:43.255000+00:00",
  "referenced_message": null,
  "mentions": [],
  "attachments": [],
  "reactions": []
},
{
  "id": "1361632285161689090",
  "author": {
    "id": 140880190158012417,
    "username": "cubox",
    "display_name": "Cubox"
  },
  "content": "<:peepocute:1263676783568621638>",
  "timestamp": "2025-04-15T09:20:34.687000+00:00",
  "referenced_message": "1361629545098117140",
  "mentions": [
    {
      "id": 362198518129098752,
      "username": "l_o_m",
      "display_name": "lom"
    }
  ],
  "attachments": [],
  "reactions": []
},
{
  "id": "1362107926931833185",
  "author": {
    "id": 362198518129098752,
    "username": "l_o_m",
    "display_name": "lom"
  },
  "content": "why does it cost less to take a TGV than a TER 💀",
  "timestamp": "2025-04-16T16:50:36.522000+00:00",
  "referenced_message": null,
  "mentions": [],
  "attachments": [],
  "reactions": []
},
{
  "id": "1362107940953260272",
  "author": {
    "id": 362198518129098752,
    "username": "l_o_m",
    "display_name": "lom"
  },
  "content": "France is weird",
  "timestamp": "2025-04-16T16:50:39.865000+00:00",
  "referenced_message": null,
  "mentions": [],
  "attachments": [],
  "reactions": []
},
{
  "id": "1366501805902532773",
  "author": {
    "id": 529303103015485441,
    "username": "thamle",
    "display_name": "Math"
  },
  "content": "<@140880190158012417>",
  "timestamp": "2025-04-28T19:50:18.903000+00:00",
  "referenced_message": null,
  "mentions": [
    {
      "id": 140880190158012417,
      "username": "cubox",
      "display_name": "Cubox"
    }
  ],
  "attachments": [],
  "reactions": []
},
{
  "id": "1366502090003841055",
  "author": {
    "id": 140880190158012417,
    "username": "cubox",
    "display_name": "Cubox"
  },
  "content": "Those keyboards are lost to the cat",
  "timestamp": "2025-04-28T19:51:26.638000+00:00",
  "referenced_message": null,
  "mentions": [],
  "attachments": [],
  "reactions": []
},
{
  "id": "1366504973168283720",
  "author": {
    "id": 529303103015485441,
    "username": "thamle",
    "display_name": "Math"
  },
  "content": "She is working for IT team",
  "timestamp": "2025-04-28T20:02:54.038000+00:00",
  "referenced_message": null,
  "mentions": [],
  "attachments": [],
  "reactions": []
},
{
  "id": "1366505025572180104",
  "author": {
    "id": 529303103015485441,
    "username": "thamle",
    "display_name": "Math"
  },
  "content": "We are back to old address soon",
  "timestamp": "2025-04-28T20:03:06.532000+00:00",
  "referenced_message": null,
  "mentions": [],
  "attachments": [],
  "reactions": []
},
{
  "id": "1366505038549094402",
  "author": {
    "id": 529303103015485441,
    "username": "thamle",
    "display_name": "Math"
  },
  "content": "Very soon",
  "timestamp": "2025-04-28T20:03:09.626000+00:00",
  "referenced_message": null,
  "mentions": [],
  "attachments": [],
  "reactions": []
},
{
  "id": "1366508408534077440",
  "author": {
    "id": 362198518129098752,
    "username": "l_o_m",
    "display_name": "lom"
  },
  "content": "<:pandanote:918001802333540362> cat",
  "timestamp": "2025-04-28T20:16:33.093000+00:00",
  "referenced_message": null,
  "mentions": [],
  "attachments": [],
  "reactions": []
},
{
  "id": "1367171008892764232",
  "author": {
    "id": 587899633456513036,
    "username": "saolapoggers",
    "display_name": "Ola"
  },
  "content": "<@692329177805881395> \nhttps://youtu.be/bQYdc-FxeYg\nI saw you downloaded Taskmaster on the Jelly some (several) months ago\nAnyway here is a funny video made by a season 19 contestant\nI wanted to share it with someone <:peepocute:1263676783568621638>",
  "timestamp": "2025-04-30T16:09:29.329000+00:00",
  "referenced_message": null,
  "mentions": [
    {
      "id": 692329177805881395,
      "username": "poulpyyyw",
      "display_name": "AntiNuggetsQueen"
    }
  ],
  "attachments": [],
  "reactions": []
},
{
  "id": "1367972696020877332",
  "author": {
    "id": 140880190158012417,
    "username": "cubox",
    "display_name": "Cubox"
  },
  "content": "https://www.reddit.com/r/funny/comments/1kd41cp/_/",
  "timestamp": "2025-05-02T21:15:06.435000+00:00",
  "referenced_message": null,
  "mentions": [],
  "attachments": [],
  "reactions": []
},
{
  "id": "1367972735657050253",
  "author": {
    "id": 140880190158012417,
    "username": "cubox",
    "display_name": "Cubox"
  },
  "content": "<@692329177805881395> <@529303103015485441> Sam's dream",
  "timestamp": "2025-05-02T21:15:15.885000+00:00",
  "referenced_message": null,
  "mentions": [
    {
      "id": 529303103015485441,
      "username": "thamle",
      "display_name": "Math"
    },
    {
      "id": 692329177805881395,
      "username": "poulpyyyw",
      "display_name": "AntiNuggetsQueen"
    }
  ],
  "attachments": [],
  "reactions": []
},
{
  "id": "1369928099621503058",
  "author": {
    "id": 692329177805881395,
    "username": "poulpyyyw",
    "display_name": "AntiNuggetsQueen"
  },
  "content": "Happy armistice day (exempt for our German friends) <:KEKW:918001534376230922>",
  "timestamp": "2025-05-08T06:45:10.983000+00:00",
  "referenced_message": null,
  "mentions": [],
  "attachments": [],
  "reactions": [
    {
      "emoji": "<:KEKW:918001534376230922>",
      "users": [
        {
          "id": 587899633456513036,
          "username": "saolapoggers",
          "display_name": "Ola"
        }
      ]
    }
  ]
},
{
  "id": "1369929498111377449",
  "author": {
    "id": 362198518129098752,
    "username": "l_o_m",
    "display_name": "lom"
  },
  "content": "<:xita:1018997092611526706>",
  "timestamp": "2025-05-08T06:50:44.409000+00:00",
  "referenced_message": null,
  "mentions": [],
  "attachments": [],
  "reactions": []
},
{
  "id": "1370064988823097515",
  "author": {
    "id": 727302962917015612,
    "username": "ytrk",
    "display_name": "Still Bavarian"
  },
  "content": "V-E day <:pandanote:918001802333540362>",
  "timestamp": "2025-05-08T15:49:07.912000+00:00",
  "referenced_message": null,
  "mentions": [],
  "attachments": [],
  "reactions": []
},
{
  "id": "1370434720713805995",
  "author": {
    "id": 529303103015485441,
    "username": "thamle",
    "display_name": "Math"
  },
  "content": "My Vietnamese workmate ask if German feel bad about this day like a national defeat day? I don't think so but maybe he is right",
  "timestamp": "2025-05-09T16:18:18.864000+00:00",
  "referenced_message": null,
  "mentions": [],
  "attachments": [],
  "reactions": []
},
{
  "id": "1370434936405758063",
  "author": {
    "id": 587899633456513036,
    "username": "saolapoggers",
    "display_name": "Ola"
  },
  "content": "<:rooVVV:981660256386052126> depends what kind of Germans you ask to",
  "timestamp": "2025-05-09T16:19:10.289000+00:00",
  "referenced_message": null,
  "mentions": [],
  "attachments": [],
  "reactions": []
},
{
  "id": "1370435171156758610",
  "author": {
    "id": 529303103015485441,
    "username": "thamle",
    "display_name": "Math"
  },
  "content": "One small part of vietnamese still hold idea that out reunion day is the defeat day of them ( Capitalism vs Communism).",
  "timestamp": "2025-05-09T16:20:06.258000+00:00",
  "referenced_message": null,
  "mentions": [],
  "attachments": [],
  "reactions": []
},
{
  "id": "1370435231722639462",
  "author": {
    "id": 529303103015485441,
    "username": "thamle",
    "display_name": "Math"
  },
  "content": "So I don't know, what is the consensus?",
  "timestamp": "2025-05-09T16:20:20.698000+00:00",
  "referenced_message": null,
  "mentions": [],
  "attachments": [],
  "reactions": []
},
{
  "id": "1370435275729404075",
  "author": {
    "id": 529303103015485441,
    "username": "thamle",
    "display_name": "Math"
  },
  "content": "A normal German",
  "timestamp": "2025-05-09T16:20:31.190000+00:00",
  "referenced_message": "1370434936405758063",
  "mentions": [
    {
      "id": 587899633456513036,
      "username": "saolapoggers",
      "display_name": "Ola"
    }
  ],
  "attachments": [],
  "reactions": []
},
{
  "id": "1370435315717636130",
  "author": {
    "id": 529303103015485441,
    "username": "thamle",
    "display_name": "Math"
  },
  "content": "Not neo nazis one",
  "timestamp": "2025-05-09T16:20:40.724000+00:00",
  "referenced_message": null,
  "mentions": [],
  "attachments": [],
  "reactions": []
},
{
  "id": "1370435984319315999",
  "author": {
    "id": 587899633456513036,
    "username": "saolapoggers",
    "display_name": "Ola"
  },
  "content": "Well I'm not German so I'm not sure",
  "timestamp": "2025-05-09T16:23:20.131000+00:00",
  "referenced_message": null,
  "mentions": [],
  "attachments": [],
  "reactions": []
},
{
  "id": "1370436014556053584",
  "author": {
    "id": 587899633456513036,
    "username": "saolapoggers",
    "display_name": "Ola"
  },
  "content": "<@140880190158012417> What do you think",
  "timestamp": "2025-05-09T16:23:27.340000+00:00",
  "referenced_message": null,
  "mentions": [
    {
      "id": 140880190158012417,
      "username": "cubox",
      "display_name": "Cubox"
    }
  ],
  "attachments": [],
  "reactions": []
},
{
  "id": "1370438398535401614",
  "author": {
    "id": 140880190158012417,
    "username": "cubox",
    "display_name": "Cubox"
  },
  "content": "You'd better ask a real germans",
  "timestamp": "2025-05-09T16:32:55.725000+00:00",
  "referenced_message": "1370436014556053584",
  "mentions": [
    {
      "id": 587899633456513036,
      "username": "saolapoggers",
      "display_name": "Ola"
    }
  ],
  "attachments": [],
  "reactions": []
},
{
  "id": "1370450257292431632",
  "author": {
    "id": 362198518129098752,
    "username": "l_o_m",
    "display_name": "lom"
  },
  "content": "most Germans view it as a day of liberation",
  "timestamp": "2025-05-09T17:20:03.073000+00:00",
  "referenced_message": "1370434720713805995",
  "mentions": [
    {
      "id": 529303103015485441,
      "username": "thamle",
      "display_name": "Math"
    }
  ],
  "attachments": [],
  "reactions": []
},
{
  "id": "1370450327979036742",
  "author": {
    "id": 362198518129098752,
    "username": "l_o_m",
    "display_name": "lom"
  },
  "content": "but there's enough who view it as a day of defeat that it's not a national holiday",
  "timestamp": "2025-05-09T17:20:19.926000+00:00",
  "referenced_message": null,
  "mentions": [],
  "attachments": [],
  "reactions": []
},
{
  "id": "1370450396874543124",
  "author": {
    "id": 362198518129098752,
    "username": "l_o_m",
    "display_name": "lom"
  },
  "content": "I mean especially historically, it took a long time until it was viewed like this",
  "timestamp": "2025-05-09T17:20:36.352000+00:00",
  "referenced_message": null,
  "mentions": [],
  "attachments": [],
  "reactions": []
},
{
  "id": "1370450498217312298",
  "author": {
    "id": 362198518129098752,
    "username": "l_o_m",
    "display_name": "lom"
  },
  "content": "I also wouldn't say that it's viewed positively, but probably because it's more seen as a day of remembrance",
  "timestamp": "2025-05-09T17:21:00.514000+00:00",
  "referenced_message": null,
  "mentions": [],
  "attachments": [],
  "reactions": []
},
{
  "id": "1370450930025234495",
  "author": {
    "id": 362198518129098752,
    "username": "l_o_m",
    "display_name": "lom"
  },
  "content": "<@248184987193442314> what's your opinion",
  "timestamp": "2025-05-09T17:22:43.465000+00:00",
  "referenced_message": null,
  "mentions": [
    {
      "id": 248184987193442314,
      "username": "magma._.",
      "display_name": "magam"
    }
  ],
  "attachments": [],
  "reactions": []
},
{
  "id": "1370450987369889794",
  "author": {
    "id": 362198518129098752,
    "username": "l_o_m",
    "display_name": "lom"
  },
  "content": "I also asked a irl friend about this today and this was the consensus we came to",
  "timestamp": "2025-05-09T17:22:57.137000+00:00",
  "referenced_message": null,
  "mentions": [],
  "attachments": [],
  "reactions": []
},
{
  "id": "1370451194626969630",
  "author": {
    "id": 587899633456513036,
    "username": "saolapoggers",
    "display_name": "Ola"
  },
  "content": "<:rooVVV:981660256386052126>",
  "timestamp": "2025-05-09T17:23:46.551000+00:00",
  "referenced_message": null,
  "mentions": [],
  "attachments": [],
  "reactions": []
},
{
  "id": "1370451228869529731",
  "author": {
    "id": 362198518129098752,
    "username": "l_o_m",
    "display_name": "lom"
  },
  "content": "wdym",
  "timestamp": "2025-05-09T17:23:54.715000+00:00",
  "referenced_message": null,
  "mentions": [],
  "attachments": [],
  "reactions": []
},
{
  "id": "1370498554518306991",
  "author": {
    "id": 248184987193442314,
    "username": "magma._.",
    "display_name": "magam"
  },
  "content": "My only problem with calling it a liberation day is that it undersells the amount of complicity by the general public. That's why I agree with the concept of remembrance day",
  "timestamp": "2025-05-09T20:31:58.029000+00:00",
  "referenced_message": null,
  "mentions": [],
  "attachments": [],
  "reactions": [
    {
      "emoji": "✅",
      "users": [
        {
          "id": 362198518129098752,
          "username": "l_o_m",
          "display_name": "lom"
        }
      ]
    }
  ]
},
{
  "id": "1370682801430728735",
  "author": {
    "id": 248184987193442314,
    "username": "magma._.",
    "display_name": "magam"
  },
  "content": "https://www.bundespraesident.de/SharedDocs/Reden/DE/Richard-von-Weizsaecker/Reden/1985/05/19850508_Rede.html",
  "timestamp": "2025-05-10T08:44:05.916000+00:00",
  "referenced_message": null,
  "mentions": [],
  "attachments": [],
  "reactions": []
},
{
  "id": "1370682935413444698",
  "author": {
    "id": 248184987193442314,
    "username": "magma._.",
    "display_name": "magam"
  },
  "content": "A speech you should definitely read if you're interested in how it's perceived in Germany generally",
  "timestamp": "2025-05-10T08:44:37.860000+00:00",
  "referenced_message": null,
  "mentions": [],
  "attachments": [],
  "reactions": []
},
{
  "id": "1370683123557339146",
  "author": {
    "id": 248184987193442314,
    "username": "magma._.",
    "display_name": "magam"
  },
  "content": "At least it shaped the public discourse",
  "timestamp": "2025-05-10T08:45:22.717000+00:00",
  "referenced_message": null,
  "mentions": [],
  "attachments": [],
  "reactions": []
},
{
  "id": "1370797817303531680",
  "author": {
    "id": 727302962917015612,
    "username": "ytrk",
    "display_name": "Still Bavarian"
  },
  "content": "good read 👍",
  "timestamp": "2025-05-10T16:21:07.837000+00:00",
  "referenced_message": null,
  "mentions": [],
  "attachments": [],
  "reactions": []
},
{
  "id": "1371287907389669447",
  "author": {
    "id": 697517830643515392,
    "username": "awgeez5107",
    "display_name": "awgeez"
  },
  "content": "to any europeans, this seems kinda important https://eci.ec.europa.eu/043/public/#/screen/home",
  "timestamp": "2025-05-12T00:48:34.419000+00:00",
  "referenced_message": null,
  "mentions": [],
  "attachments": [],
  "reactions": []
},
{
  "id": "1372384763742126082",
  "author": {
    "id": 140880190158012417,
    "username": "cubox",
    "display_name": "Cubox"
  },
  "content": "https://www.reddit.com/r/interestingasfuck/comments/1kmsb99/_/",
  "timestamp": "2025-05-15T01:27:05.352000+00:00",
  "referenced_message": null,
  "mentions": [],
  "attachments": [],
  "reactions": []
},
{
  "id": "1372384808021262407",
  "author": {
    "id": 140880190158012417,
    "username": "cubox",
    "display_name": "Cubox"
  },
  "content": "Found <@692329177805881395> 's bedroom",
  "timestamp": "2025-05-15T01:27:15.909000+00:00",
  "referenced_message": null,
  "mentions": [
    {
      "id": 692329177805881395,
      "username": "poulpyyyw",
      "display_name": "AntiNuggetsQueen"
    }
  ],
  "attachments": [],
  "reactions": []
},
{
  "id": "1372385716520222781",
  "author": {
    "id": 727302962917015612,
    "username": "ytrk",
    "display_name": "Still Bavarian"
  },
  "content": "Jk rowling",
  "timestamp": "2025-05-15T01:30:52.512000+00:00",
  "referenced_message": null,
  "mentions": [],
  "attachments": [],
  "reactions": [
    {
      "emoji": "<:KEKW:918001534376230922>",
      "users": [
        {
          "id": 692329177805881395,
          "username": "poulpyyyw",
          "display_name": "AntiNuggetsQueen"
        }
      ]
    }
  ]
},
{
  "id": "1372961432609751071",
  "author": {
    "id": 140880190158012417,
    "username": "cubox",
    "display_name": "Cubox"
  },
  "content": "wow it just got it",
  "timestamp": "2025-05-16T15:38:33.924000+00:00",
  "referenced_message": "1371287907389669447",
  "mentions": [
    {
      "id": 697517830643515392,
      "username": "awgeez5107",
      "display_name": "awgeez"
    }
  ],
  "attachments": [],
  "reactions": []
},
{
  "id": "1372961743147368468",
  "author": {
    "id": 140880190158012417,
    "username": "cubox",
    "display_name": "Cubox"
  },
  "content": "half a million from france",
  "timestamp": "2025-05-16T15:39:47.962000+00:00",
  "referenced_message": null,
  "mentions": [],
  "attachments": [],
  "reactions": []
},
{
  "id": "1372964486948061314",
  "author": {
    "id": 114881818922188801,
    "username": "aydemphia",
    "display_name": "aydemphia"
  },
  "content": "Yep almost 1% of french citizens",
  "timestamp": "2025-05-16T15:50:42.135000+00:00",
  "referenced_message": null,
  "mentions": [],
  "attachments": [],
  "reactions": []
},
{
  "id": "1372967152914661438",
  "author": {
    "id": 697517830643515392,
    "username": "awgeez5107",
    "display_name": "awgeez"
  },
  "content": "<:poggies:918020176849543198>",
  "timestamp": "2025-05-16T16:01:17.751000+00:00",
  "referenced_message": null,
  "mentions": [],
  "attachments": [],
  "reactions": []
},
{
  "id": "1372990701553455104",
  "author": {
    "id": 692329177805881395,
    "username": "poulpyyyw",
    "display_name": "AntiNuggetsQueen"
  },
  "content": "France is woke",
  "timestamp": "2025-05-16T17:34:52.184000+00:00",
  "referenced_message": "1372961743147368468",
  "mentions": [
    {
      "id": 140880190158012417,
      "username": "cubox",
      "display_name": "Cubox"
    }
  ],
  "attachments": [],
  "reactions": []
},
{
  "id": "1372990771757584605",
  "author": {
    "id": 114881818922188801,
    "username": "aydemphia",
    "display_name": "aydemphia"
  },
  "content": "😪",
  "timestamp": "2025-05-16T17:35:08.922000+00:00",
  "referenced_message": null,
  "mentions": [],
  "attachments": [],
  "reactions": []
},
{
  "id": "1372991394620379207",
  "author": {
    "id": 114881818922188801,
    "username": "aydemphia",
    "display_name": "aydemphia"
  },
  "content": "This ad makes me laugh",
  "timestamp": "2025-05-16T17:37:37.424000+00:00",
  "referenced_message": null,
  "mentions": [],
  "attachments": [],
  "reactions": []
},
{
  "id": "1372991668378140854",
  "author": {
    "id": 692329177805881395,
    "username": "poulpyyyw",
    "display_name": "AntiNuggetsQueen"
  },
  "content": "It can happen to all of us",
  "timestamp": "2025-05-16T17:38:42.693000+00:00",
  "referenced_message": "1372991394620379207",
  "mentions": [
    {
      "id": 114881818922188801,
      "username": "aydemphia",
      "display_name": "aydemphia"
    }
  ],
  "attachments": [],
  "reactions": []
},
{
  "id": "1373311865781883093",
  "author": {
    "id": 114881818922188801,
    "username": "aydemphia",
    "display_name": "aydemphia"
  },
  "content": "John is exceptional",
  "timestamp": "2025-05-17T14:51:03.703000+00:00",
  "referenced_message": null,
  "mentions": [],
  "attachments": [],
  "reactions": []
},
{
  "id": "1373311956328386660",
  "author": {
    "id": 114881818922188801,
    "username": "aydemphia",
    "display_name": "aydemphia"
  },
  "content": "Of course everything I said there is pure comedy 😵‍💫",
  "timestamp": "2025-05-17T14:51:25.291000+00:00",
  "referenced_message": null,
  "mentions": [],
  "attachments": [],
  "reactions": []
},
{
  "id": "1373336079478030367",
  "author": {
    "id": 140880190158012417,
    "username": "cubox",
    "display_name": "Cubox"
  },
  "content": "We know deep down you're as bigoted as you're pretending there for \"comedy\"",
  "timestamp": "2025-05-17T16:27:16.698000+00:00",
  "referenced_message": null,
  "mentions": [],
  "attachments": [],
  "reactions": []
},
{
  "id": "1373382894134886450",
  "author": {
    "id": 692329177805881395,
    "username": "poulpyyyw",
    "display_name": "AntiNuggetsQueen"
  },
  "content": "Who is watching Eurovision?",
  "timestamp": "2025-05-17T19:33:18.182000+00:00",
  "referenced_message": null,
  "mentions": [],
  "attachments": [],
  "reactions": []
},
{
  "id": "1373382970030948383",
  "author": {
    "id": 140880190158012417,
    "username": "cubox",
    "display_name": "Cubox"
  },
  "content": "Nerd",
  "timestamp": "2025-05-17T19:33:36.277000+00:00",
  "referenced_message": null,
  "mentions": [],
  "attachments": [],
  "reactions": []
},
{
  "id": "1373386104866209854",
  "author": {
    "id": 114881818922188801,
    "username": "aydemphia",
    "display_name": "aydemphia"
  },
  "content": "Me",
  "timestamp": "2025-05-17T19:46:03.680000+00:00",
  "referenced_message": null,
  "mentions": [],
  "attachments": [],
  "reactions": []
},
{
  "id": "1373439608565792859",
  "author": {
    "id": 612660205913899029,
    "username": "mansimum",
    "display_name": "Väinämöinen"
  },
  "content": "Did we get last place again? <:pandanote:918001802333540362>",
  "timestamp": "2025-05-17T23:18:39.956000+00:00",
  "referenced_message": null,
  "mentions": [],
  "attachments": [],
  "reactions": []
},
{
  "id": "1373584603528429578",
  "author": {
    "id": 692329177805881395,
    "username": "poulpyyyw",
    "display_name": "AntiNuggetsQueen"
  },
  "content": "Who is we .",
  "timestamp": "2025-05-18T08:54:49.449000+00:00",
  "referenced_message": "1373439608565792859",
  "mentions": [
    {
      "id": 612660205913899029,
      "username": "mansimum",
      "display_name": "Väinämöinen"
    }
  ],
  "attachments": [],
  "reactions": []
},
{
  "id": "1373585631254675457",
  "author": {
    "id": 362198518129098752,
    "username": "l_o_m",
    "display_name": "lom"
  },
  "content": "Germany <:rooVVV:981660256386052126>",
  "timestamp": "2025-05-18T08:58:54.478000+00:00",
  "referenced_message": null,
  "mentions": [],
  "attachments": [],
  "reactions": []
},
{
  "id": "1373588812587864065",
  "author": {
    "id": 114881818922188801,
    "username": "aydemphia",
    "display_name": "aydemphia"
  },
  "content": "No u were in front of the UK",
  "timestamp": "2025-05-18T09:11:32.967000+00:00",
  "referenced_message": null,
  "mentions": [],
  "attachments": [],
  "reactions": []
},
{
  "id": "1373588905403613255",
  "author": {
    "id": 114881818922188801,
    "username": "aydemphia",
    "display_name": "aydemphia"
  },
  "content": "It's not a big win but that's already conforting",
  "timestamp": "2025-05-18T09:11:55.096000+00:00",
  "referenced_message": null,
  "mentions": [],
  "attachments": [],
  "reactions": []
},
{
  "id": "1373592808606404689",
  "author": {
    "id": 612660205913899029,
    "username": "mansimum",
    "display_name": "Väinämöinen"
  },
  "content": "the streak has ended <:sadge:918020221237862421>",
  "timestamp": "2025-05-18T09:27:25.692000+00:00",
  "referenced_message": null,
  "mentions": [],
  "attachments": [],
  "reactions": []
},
{
  "id": "1376258783789060219",
  "author": {
    "id": 140880190158012417,
    "username": "cubox",
    "display_name": "Cubox"
  },
  "content": "<@692329177805881395>",
  "timestamp": "2025-05-25T18:01:03.707000+00:00",
  "referenced_message": null,
  "mentions": [
    {
      "id": 692329177805881395,
      "username": "poulpyyyw",
      "display_name": "AntiNuggetsQueen"
    }
  ],
  "attachments": [
    "<attachment>"
  ],
  "reactions": []
},
{
  "id": "1376540595090096280",
  "author": {
    "id": 177553498139918347,
    "username": "josuald",
    "display_name": "cchicote"
  },
  "content": "Saw this very good 12 years old boi on the street \n\nTotally fell in love with him",
  "timestamp": "2025-05-26T12:40:52.757000+00:00",
  "referenced_message": null,
  "mentions": [],
  "attachments": [],
  "reactions": []
},
{
  "id": "1376541735945240627",
  "author": {
    "id": 140880190158012417,
    "username": "cubox",
    "display_name": "Cubox"
  },
  "content": "<:roolove:918020195753279489>",
  "timestamp": "2025-05-26T12:45:24.758000+00:00",
  "referenced_message": "1376540521257898044",
  "mentions": [
    {
      "id": 177553498139918347,
      "username": "josuald",
      "display_name": "cchicote"
    }
  ],
  "attachments": [],
  "reactions": []
},
{
  "id": "1376725944538239036",
  "author": {
    "id": 140880190158012417,
    "username": "cubox",
    "display_name": "Cubox"
  },
  "content": "Me and blahaj",
  "timestamp": "2025-05-27T00:57:23.509000+00:00",
  "referenced_message": null,
  "mentions": [],
  "attachments": [
    "<attachment>"
  ],
  "reactions": [
    {
      "emoji": "<:rooVVV:981660256386052126>",
      "users": [
        {
          "id": 587899633456513036,
          "username": "saolapoggers",
          "display_name": "Ola"
        }
      ]
    },
    {
      "emoji": "<:peepocute:1263676783568621638>",
      "users": [
        {
          "id": 1089928222252208170,
          "username": "autumnprincess",
          "display_name": "kanthru"
        },
        {
          "id": 692329177805881395,
          "username": "poulpyyyw",
          "display_name": "AntiNuggetsQueen"
        },
        {
          "id": 587899633456513036,
          "username": "saolapoggers",
          "display_name": "Ola"
        },
        {
          "id": 489569435812757519,
          "username": "dread2much",
          "display_name": "dread"
        },
        {
          "id": 185509563787771904,
          "username": "glour",
          "display_name": "Glour"
        }
      ]
    },
    {
      "emoji": "<:pandaww:918019879456636978>",
      "users": [
        {
          "id": 1089928222252208170,
          "username": "autumnprincess",
          "display_name": "kanthru"
        },
        {
          "id": 587899633456513036,
          "username": "saolapoggers",
          "display_name": "Ola"
        }
      ]
    }
  ]
},
{
  "id": "1379058852603101287",
  "author": {
    "id": 140880190158012417,
    "username": "cubox",
    "display_name": "Cubox"
  },
  "content": "Me when I talk about F1 to <@692329177805881395> or about nerd video game stuff to <@529303103015485441>",
  "timestamp": "2025-06-02T11:27:32.135000+00:00",
  "referenced_message": null,
  "mentions": [
    {
      "id": 529303103015485441,
      "username": "thamle",
      "display_name": "Math"
    },
    {
      "id": 692329177805881395,
      "username": "poulpyyyw",
      "display_name": "AntiNuggetsQueen"
    }
  ],
  "attachments": [
    "<attachment>"
  ],
  "reactions": [
    {
      "emoji": "<:KEKW:918001534376230922>",
      "users": [
        {
          "id": 529303103015485441,
          "username": "thamle",
          "display_name": "Math"
        }
      ]
    }
  ]
},
{
  "id": "1379182985252831303",
  "author": {
    "id": 140880190158012417,
    "username": "cubox",
    "display_name": "Cubox"
  },
  "content": ":(",
  "timestamp": "2025-06-02T19:40:47.665000+00:00",
  "referenced_message": null,
  "mentions": [],
  "attachments": [
    "<attachment>"
  ],
  "reactions": [
    {
      "emoji": "<:xita:1018997092611526706>",
      "users": [
        {
          "id": 142496872152432651,
          "username": "erinisafox",
          "display_name": "Audrey"
        }
      ]
    }
  ]
},
{
  "id": "1379192164780867624",
  "author": {
    "id": 587899633456513036,
    "username": "saolapoggers",
    "display_name": "Ola"
  },
  "content": "Surely they didn't have those diagonal trenches even back then?",
  "timestamp": "2025-06-02T20:17:16.235000+00:00",
  "referenced_message": null,
  "mentions": [],
  "attachments": [],
  "reactions": []
},
{
  "id": "1379192219973582910",
  "author": {
    "id": 587899633456513036,
    "username": "saolapoggers",
    "display_name": "Ola"
  },
  "content": "I mean the trenches on the baguette, I don't know how to call them",
  "timestamp": "2025-06-02T20:17:29.394000+00:00",
  "referenced_message": null,
  "mentions": [],
  "attachments": [],
  "reactions": []
},
{
  "id": "1379192534928199710",
  "author": {
    "id": 140880190158012417,
    "username": "cubox",
    "display_name": "Cubox"
  },
  "content": "who's a bread expert we could ask",
  "timestamp": "2025-06-02T20:18:44.485000+00:00",
  "referenced_message": null,
  "mentions": [],
  "attachments": [],
  "reactions": []
},
{
  "id": "1379192559586381905",
  "author": {
    "id": 140880190158012417,
    "username": "cubox",
    "display_name": "Cubox"
  },
  "content": "maybe we can ask John",
  "timestamp": "2025-06-02T20:18:50.364000+00:00",
  "referenced_message": null,
  "mentions": [],
  "attachments": [],
  "reactions": []
},
{
  "id": "1379194526605774979",
  "author": {
    "id": 248184987193442314,
    "username": "magma._.",
    "display_name": "magam"
  },
  "content": "it's not really advanced tech <:KEKW:918001534376230922>",
  "timestamp": "2025-06-02T20:26:39.338000+00:00",
  "referenced_message": "1379192164780867624",
  "mentions": [],
  "attachments": [],
  "reactions": []
},
{
  "id": "1379217445885186138",
  "author": {
    "id": 587899633456513036,
    "username": "saolapoggers",
    "display_name": "Ola"
  },
  "content": "Well sure",
  "timestamp": "2025-06-02T21:57:43.720000+00:00",
  "referenced_message": null,
  "mentions": [],
  "attachments": [],
  "reactions": []
},
{
  "id": "1379217509449990214",
  "author": {
    "id": 587899633456513036,
    "username": "saolapoggers",
    "display_name": "Ola"
  },
  "content": "But I wouldn't think they'd care",
  "timestamp": "2025-06-02T21:57:58.875000+00:00",
  "referenced_message": null,
  "mentions": [],
  "attachments": [],
  "reactions": []
},
{
  "id": "1379221046925987991",
  "author": {
    "id": 1089928222252208170,
    "username": "autumnprincess",
    "display_name": "kanthru"
  },
  "content": "People not caring about bread?? We’ve been caring too much about bread forever",
  "timestamp": "2025-06-02T22:12:02.275000+00:00",
  "referenced_message": "1379217509449990214",
  "mentions": [
    {
      "id": 587899633456513036,
      "username": "saolapoggers",
      "display_name": "Ola"
    }
  ],
  "attachments": [
    "<attachment>"
  ],
  "reactions": []
},
{
  "id": "1379221385402122410",
  "author": {
    "id": 140880190158012417,
    "username": "cubox",
    "display_name": "Cubox"
  },
  "content": "<@1089928222252208170> <:roolove:918020195753279489>",
  "timestamp": "2025-06-02T22:13:22.974000+00:00",
  "referenced_message": null,
  "mentions": [
    {
      "id": 1089928222252208170,
      "username": "autumnprincess",
      "display_name": "kanthru"
    }
  ],
  "attachments": [],
  "reactions": []
},
{
  "id": "1379221438858268693",
  "author": {
    "id": 1089928222252208170,
    "username": "autumnprincess",
    "display_name": "kanthru"
  },
  "content": "<:roolove:918020195753279489>",
  "timestamp": "2025-06-02T22:13:35.719000+00:00",
  "referenced_message": "1379221385402122410",
  "mentions": [
    {
      "id": 140880190158012417,
      "username": "cubox",
      "display_name": "Cubox"
    }
  ],
  "attachments": [],
  "reactions": []
},
{
  "id": "1379460517500026981",
  "author": {
    "id": 1089928222252208170,
    "username": "autumnprincess",
    "display_name": "kanthru"
  },
  "content": "https://cdn.discordapp.com/attachments/346250610020057088/1379273458025758811/image0.gif",
  "timestamp": "2025-06-03T14:03:36.509000+00:00",
  "referenced_message": null,
  "mentions": [],
  "attachments": [],
  "reactions": []
},
{
  "id": "1379483599794864301",
  "author": {
    "id": 140880190158012417,
    "username": "cubox",
    "display_name": "Cubox"
  },
  "content": "Have you played grounded",
  "timestamp": "2025-06-03T15:35:19.757000+00:00",
  "referenced_message": "1379460517500026981",
  "mentions": [
    {
      "id": 1089928222252208170,
      "username": "autumnprincess",
      "display_name": "kanthru"
    }
  ],
  "attachments": [],
  "reactions": []
},
{
  "id": "1379486869783445534",
  "author": {
    "id": 1089928222252208170,
    "username": "autumnprincess",
    "display_name": "kanthru"
  },
  "content": "I have not! ive never even heard of it",
  "timestamp": "2025-06-03T15:48:19.383000+00:00",
  "referenced_message": null,
  "mentions": [],
  "attachments": [],
  "reactions": []
},
{
  "id": "1379487031234924735",
  "author": {
    "id": 114881818922188801,
    "username": "aydemphia",
    "display_name": "aydemphia"
  },
  "content": "is it the ant simulator thing ?",
  "timestamp": "2025-06-03T15:48:57.876000+00:00",
  "referenced_message": null,
  "mentions": [],
  "attachments": [],
  "reactions": []
},
{
  "id": "1379487043767242894",
  "author": {
    "id": 140880190158012417,
    "username": "cubox",
    "display_name": "Cubox"
  },
  "content": "you can kill weevils in it",
  "timestamp": "2025-06-03T15:49:00.864000+00:00",
  "referenced_message": null,
  "mentions": [],
  "attachments": [],
  "reactions": []
},
{
  "id": "1379487129503272960",
  "author": {
    "id": 140880190158012417,
    "username": "cubox",
    "display_name": "Cubox"
  },
  "content": "survival where you're small yes",
  "timestamp": "2025-06-03T15:49:21.305000+00:00",
  "referenced_message": "1379487031234924735",
  "mentions": [
    {
      "id": 114881818922188801,
      "username": "aydemphia",
      "display_name": "aydemphia"
    }
  ],
  "attachments": [],
  "reactions": []
},
{
  "id": "1379487997401235514",
  "author": {
    "id": 114881818922188801,
    "username": "aydemphia",
    "display_name": "aydemphia"
  },
  "content": "oh",
  "timestamp": "2025-06-03T15:52:48.228000+00:00",
  "referenced_message": null,
  "mentions": [],
  "attachments": [],
  "reactions": []
},
{
  "id": "1379488046088720455",
  "author": {
    "id": 114881818922188801,
    "username": "aydemphia",
    "display_name": "aydemphia"
  },
  "content": "i was thinking it was \"empire of the underground\"",
  "timestamp": "2025-06-03T15:52:59.836000+00:00",
  "referenced_message": null,
  "mentions": [],
  "attachments": [],
  "reactions": []
},
{
  "id": "1379488224929513533",
  "author": {
    "id": 140880190158012417,
    "username": "cubox",
    "display_name": "Cubox"
  },
  "content": "https://tenor.com/view/grounded-grounded-game-stink-bug-bug-cat-gif-26906058",
  "timestamp": "2025-06-03T15:53:42.475000+00:00",
  "referenced_message": null,
  "mentions": [],
  "attachments": [],
  "reactions": [
    {
      "emoji": "<:KEKW:918001534376230922>",
      "users": [
        {
          "id": 114881818922188801,
          "username": "aydemphia",
          "display_name": "aydemphia"
        }
      ]
    }
  ]
},
{
  "id": "1379488325467115621",
  "author": {
    "id": 140880190158012417,
    "username": "cubox",
    "display_name": "Cubox"
  },
  "content": "https://tenor.com/view/zach-sap-zachsapgames-game-grounded-gif-18007191",
  "timestamp": "2025-06-03T15:54:06.445000+00:00",
  "referenced_message": null,
  "mentions": [],
  "attachments": [],
  "reactions": []
},
{
  "id": "1379488366134951986",
  "author": {
    "id": 140880190158012417,
    "username": "cubox",
    "display_name": "Cubox"
  },
  "content": "https://tenor.com/view/naux101-naux-grounded-grounded-game-twitch-gif-26032802",
  "timestamp": "2025-06-03T15:54:16.141000+00:00",
  "referenced_message": null,
  "mentions": [],
  "attachments": [],
  "reactions": []
},
{
  "id": "1379489480775565442",
  "author": {
    "id": 140880190158012417,
    "username": "cubox",
    "display_name": "Cubox"
  },
  "content": "you have to craft a gas mask to fight them",
  "timestamp": "2025-06-03T15:58:41.892000+00:00",
  "referenced_message": "1379488224929513533",
  "mentions": [],
  "attachments": [],
  "reactions": [
    {
      "emoji": "😂",
      "users": [
        {
          "id": 1216527387194298378,
          "username": "John",
          "display_name": "John"
        }
      ]
    }
  ]
},
{
  "id": "1380968174719205460",
  "author": {
    "id": 140880190158012417,
    "username": "cubox",
    "display_name": "Cubox"
  },
  "content": "<@&1179038806243029063>",
  "timestamp": "2025-06-07T17:54:30.001000+00:00",
  "referenced_message": null,
  "mentions": [],
  "attachments": [
    "<attachment>"
  ],
  "reactions": []
}
]

